#!/usr/bin/env python
"""
简单检查卡住状态
"""
import os
import sys
import django
from django.db import connection

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
django.setup()

def check_running_rooms():
    """检查Running状态的房间"""
    with connection.cursor() as cursor:
        # 统计各状态
        cursor.execute("""
            SELECT state, COUNT(*) as count 
            FROM box_caseroom 
            WHERE type IN (1, 3) 
            GROUP BY state 
            ORDER BY state
        """)
        
        print("=== 对战房间状态统计 ===")
        state_names = {1: 'Initial', 2: 'Joinable', 3: 'Joining', 4: 'Full', 5: 'Running', 11: 'End', 20: 'Cancelled'}
        
        for row in cursor.fetchall():
            state, count = row
            state_name = state_names.get(state, f'Unknown({state})')
            print(f"状态 {state} ({state_name}): {count} 个房间")
        
        # 查看Running房间详情
        cursor.execute("""
            SELECT short_id, create_time, update_time, 
                   TIMESTAMPDIFF(MINUTE, update_time, NOW()) as minutes_stuck
            FROM box_caseroom 
            WHERE state = 5 AND type IN (1, 3)
            ORDER BY update_time
            LIMIT 10
        """)
        
        running_rooms = cursor.fetchall()
        print(f"\n=== Running状态房间详情 ({len(running_rooms)}个) ===")
        
        for room in running_rooms:
            short_id, create_time, update_time, minutes_stuck = room
            print(f"房间 {short_id}: 卡住 {minutes_stuck} 分钟")
            print(f"  创建: {create_time}")
            print(f"  更新: {update_time}")
            
            # 检查轮次状态
            cursor.execute("""
                SELECT COUNT(*) as total, 
                       SUM(CASE WHEN opened = 1 THEN 1 ELSE 0 END) as opened,
                       SUM(CASE WHEN opened = 0 THEN 1 ELSE 0 END) as unopened
                FROM box_caseroomround r
                JOIN box_caseroom cr ON r.room_id = cr.id
                WHERE cr.short_id = %s
            """, [short_id])
            
            round_result = cursor.fetchone()
            if round_result:
                total, opened, unopened = round_result
                print(f"  轮次: 总{total} 已开{opened} 未开{unopened}")
                
                if unopened == 0 and total > 0:
                    print(f"  ❌ 所有轮次已完成但房间仍在Running - 需要强制结束")
                elif unopened > 0:
                    print(f"  ⚠️  有{unopened}个轮次未处理 - 需要推进处理")
            print()

if __name__ == "__main__":
    try:
        check_running_rooms()
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()
