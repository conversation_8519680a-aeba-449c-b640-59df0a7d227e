from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class BoxConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'box'
    verbose_name = _('Box')

    def ready(self):
        """应用就绪时的初始化"""
        logger.info("Box应用正在初始化...")

        try:
            # 初始化增强异步系统
            self._initialize_async_system()

            logger.info("Box应用初始化完成")

        except Exception as e:
            logger.error(f"Box应用初始化失败: {e}")

    def _initialize_async_system(self):
        """初始化增强异步系统"""
        try:
            from .enhanced_async_system import get_battle_async_processor

            # 获取对战异步处理器实例（这会自动启动它）
            processor = get_battle_async_processor()

            # 检查处理器状态
            stats = processor.get_stats()
            logger.info(f"增强异步系统已启动: {stats}")

        except Exception as e:
            logger.error(f"增强异步系统初始化失败: {e}")
