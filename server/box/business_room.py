import numpy
import json
import logging
import random
import threading
import time
import math
import uuid
from datetime import datetime
import hashlib
from django.core.cache import cache
import os

from steambase import settings
from django.contrib.auth import get_user_model
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import connection, transaction
from django.db.models import Q, F, Sum

from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from itertools import chain
from django_redis import get_redis_connection
from datetime import timedelta, datetime
# from libs.aws_sms import send_sms as aws_send_sms

from steambase.redis_con import get_redis
from steambase.enums import RespCode, CaseCategoryType, PackageState, GameState, RoomType, BetTeamType, \
    CaseCategoryType, CaseRecordType, PackageSourceType
from steambase.utils import is_connection_usable, ParamException
from steambase.utils import current_year, current_week, set_room_sid, remove_sid, delete_room, get_room_sids
from package.interfaces import get_item_price_by_name
from package.models import PackageItem, ItemInfo
from package.serializers import ItemInfoSerializer
from chat.business import get_last_chat_msg
from sitecfg.models import SiteConfig
from sitecfg.interfaces import get_box_festival_expired, get_box_festival_sms_content, get_user_point_max, get_cases_max
from sitecfg.interfaces import get_box_room_expire, get_pk_bot_interval_time, get_pk_bot_join_interval_time, get_pk_bot_num_max, get_maintenance, get_maintenance_box_room
# from charge.interfaces import get_charge_total
from box.models import Case, CaseType, CaseRecord, CaseBot, CaseStatisticsDay, CaseStatisticsMonth, RoomDayRank, GiveawayItems
from box.models import DropItem, FreeCaseConfig, FestivalCaseConfig, FestivalCaseDate, FestivalCaseRecord
from box.models import CaseRoom, CaseRoomRound, CaseRoomBet, CaseRoomItem, DropDayRank, IncomeDayRank, LoseWeekRank
from box.serializers import CaseSerializer, DropItemSerializer, CaseRecordSerializer, FreeCaseConfigSerializer, \
    CaseRoomBetSerializer, CaseRoomRoundSerializer, CaseRoomCacheSerializer
from box.serializers import CaseStatisticsDaySerializer, DropDayRankSerializer, IncomeDayRankSerializer
from box.serializers import FestivalCaseRecordSerializer, CaseRoomSerializer, RoomDayRankSerializer, \
    LoseWeekRankSerializer
from box.serializers import CaseRoomRecordSerializer, CaseRoomHistorySerializer, CaseRoomDetailSerializer
from authentication.models import AuthUser
from package.business import user_exchange_items
from box.utils import update_user_box_chance_type

from package.service.item import get_item_price_by_id


_logger = logging.getLogger(__name__)
# _box_redis_channel_key = 'box_game_channel'
_box_redis_channel_key = 'ws_channel'
_box_room_key = 'box_room:{}'
_box_room_rounds_key = 'box_rounds_rid'
USER = get_user_model()

QUICK_SELL_KEY = 'quickSell:{}:{}'

# 消息去重缓存配置
MESSAGE_DEDUP_TIMEOUT = 300  # 5分钟去重窗口
MESSAGE_DEDUP_KEY_PREFIX = 'ws_msg_dedup:'

def generate_message_hash(room_uid, message_type, round_number=None, animation_id=None):
    """生成消息唯一标识符"""
    components = [room_uid, message_type]
    if round_number is not None:
        components.append(str(round_number))
    if animation_id:
        components.append(animation_id)
    
    message_key = ':'.join(components)
    return hashlib.md5(message_key.encode()).hexdigest()

def is_message_already_sent(room_uid, message_type, round_number=None, animation_id=None):
    """检查消息是否已经发送过"""
    message_hash = generate_message_hash(room_uid, message_type, round_number, animation_id)
    cache_key = f"{MESSAGE_DEDUP_KEY_PREFIX}{message_hash}"
    
    return cache.get(cache_key) is not None

def mark_message_as_sent(room_uid, message_type, round_number=None, animation_id=None):
    """标记消息已发送"""
    message_hash = generate_message_hash(room_uid, message_type, round_number, animation_id)
    cache_key = f"{MESSAGE_DEDUP_KEY_PREFIX}{message_hash}"
    
    # 缓存消息标记，5分钟后自动过期
    cache.set(cache_key, True, MESSAGE_DEDUP_TIMEOUT)
    
    _logger.info(f"标记消息已发送: {message_type}, room={room_uid}, round={round_number}, hash={message_hash}")

def ws_send_box_game(data, action):
    if data:
        r = get_redis_connection('default')
        # 确保数据类型正确，避免序列化错误
        try:
            # 清理数据，确保所有值都可序列化
            sanitized_data = _sanitize_websocket_data(data)
            rt_msg = ['box', str(action), sanitized_data]
            r.publish(_box_redis_channel_key, json.dumps(rt_msg, ensure_ascii=False, default=str))
        except Exception as e:
            _logger.error(f"WebSocket消息发送失败: action={action}, error={e}")


def ws_send_box_room(data, action):
    if data:
        r = get_redis_connection('default')
        try:
            # 清理数据，确保所有值都可序列化
            sanitized_data = _sanitize_websocket_data(data)
            rt_msg = ['boxroom', str(action), sanitized_data]
            r.publish(_box_redis_channel_key, json.dumps(rt_msg, ensure_ascii=False, default=str))
        except Exception as e:
            _logger.error(f"WebSocket房间消息发送失败: action={action}, error={e}")


def ws_send_box_room_detail(data, action, sid):
    if data:
        r = get_redis_connection('default')
        try:
            # 清理数据，确保所有值都可序列化
            sanitized_data = _sanitize_websocket_data(data)
            rt_msg = ['boxroomdetail', str(action), sanitized_data, str(sid)]
            r.publish(_box_redis_channel_key, json.dumps(rt_msg, ensure_ascii=False, default=str))
        except Exception as e:
            _logger.error(f"WebSocket房间详情消息发送失败: action={action}, sid={sid}, error={e}")


def _sanitize_websocket_data(data):
    """清理WebSocket数据，确保类型安全"""
    if isinstance(data, dict):
        return {k: _sanitize_websocket_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_sanitize_websocket_data(item) for item in data]
    elif isinstance(data, (int, float, str, bool)) or data is None:
        return data
    elif hasattr(data, 'isoformat'):  # datetime对象
        return data.isoformat()
    else:
        return str(data)


def get_drop_price(drop):
    if drop.item_type == 1:
        return get_item_price_by_id(drop.item_info.id).price
    else:
        return drop.coin


def cache_quick_sell_pid(room_uid, username, pids):
    r = get_redis()
    key = QUICK_SELL_KEY.format(room_uid, username)
    # print(key, json.dumps(pids))
    # r.setex(key, 180, json.dumps(pids))
    r.setex(key, json.dumps(pids), 180)


def create_case_room(user, cases, max_joiner, room_type, private):
    def check_user_permissions(user):
        if user.extra.ban_battle == 1:                
            return RespCode.BadRequest.value, _('对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试。')
        if CaseRoom.objects.filter(
            bets__user=user,
            state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value,
                GameState.Full.value,
                GameState.Running.value,
            ]).count() >= settings.CASE_ROOM_CREATE_MAX:
            return RespCode.InvalidParams.value, _('Over max room count')
        return None

    def validate_cases(cases):
        cases_max = get_cases_max()
        if not cases or len(cases) < 1 or len(cases) > cases_max:
            return RespCode.InvalidParams.value, _('Invalid cases count')
        return None

    def validate_joiners(max_joiner):
        if not max_joiner or max_joiner < 2 or max_joiner > 4:
            return RespCode.InvalidParams.value, _('Invalid joiners count')
        return None

    def get_room_cases(cases, user):
        room_cases = []
        room_price = 0
        for key in cases:
            c = Case.objects.filter(case_key=key, is_show=True, enable=True, enable_room=True).first()
            if c:
                if not user.is_superuser:
                    if c.enable_admin:
                        return None, RespCode.InvalidGame.value, _('Case is block, please choice other case.')
                room_cases.append(c)
                cost = round(c.price * c.discount / 100, 2)
                room_price += cost
            else:
                return None, RespCode.InvalidParams.value, _('Invalid cases')
        return room_cases, room_price, None

    def check_user_balance(user, room_price):
        if user.asset.balance < room_price:
            return RespCode.InvalidParams.value, _('余额不足')
        if user.asset.balance * 0.5 < room_price:
            return RespCode.InvalidParams.value, _('对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间。')
        return None

    permission_error = check_user_permissions(user)
    if permission_error:
        return permission_error

    case_error = validate_cases(cases)
    if case_error:
        return case_error

    joiner_error = validate_joiners(max_joiner)
    if joiner_error:
        return joiner_error

    room_cases, room_price, case_error = get_room_cases(cases, user)
    if case_error:
        return case_error

    balance_error = check_user_balance(user, room_price)
    if balance_error:
        return balance_error

    room_info = {
        'user': user,
        'max_joiner': max_joiner,
        'price': room_price,
        'type': room_type,
        'private': private
    }

    with transaction.atomic():
        user.update_balance(-room_info['price'], _('创建对战'))
        room = CaseRoom.objects.create(**room_info)
        CaseRoomBet.objects.create(room=room, user=user)
        
        # 为每个箱子创建一个回合
        rounds_to_create = [CaseRoomRound(room=room, case=c) for c in room_cases]
        CaseRoomRound.objects.bulk_create(rounds_to_create)

        CaseRoom.objects.filter(short_id=room.short_id).update(state=GameState.Joinable.value)

        room_data = CaseRoomSerializer(room).data
        ws_send_box_room(room_data, 'new')

        # 更新对战场次统计
        try:
            update_base_count('pk_base_count', 1)
            _logger.info(f'[对战创建] 对战场次统计已更新: +1')
        except Exception as e:
            _logger.error(f'[对战创建] 更新对战场次统计失败: {e}')

        # 推送更新的统计数据（对战总数增加）
        try:
            from monitor.business import generate_monitor_stats, ws_send_monitor
            stats = generate_monitor_stats()
            ws_send_monitor(stats, 'update', 'monitor')
            _logger.info(f'[对战创建] 对战总数更新: {stats["battle_number"]}')
        except Exception as e:
            _logger.error(f'[对战创建] 推送统计数据失败: {e}')

        data = {
            'room': room.uid,
            'rid': room.short_id
        }
        return RespCode.Succeed.value, data


def join_case_room(user, uid):
    """
    加入房间
    :param user: 当前用户
    :param uid: 房间uid
    :return:
    """
    
    def check_user_permissions(user):
        if user.extra.ban_battle == 1:                
            return RespCode.BadRequest.value, _('对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试。')
        if CaseRoom.objects.filter(
            bets__user=user,
            state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value,
                GameState.Full.value,
                GameState.Running.value,
            ]).count() >= settings.CASE_ROOM_CREATE_MAX:
            
            return RespCode.InvalidParams.value, _('Over max room count')
        
        return None
    
 

    def get_room(uid):
        return CaseRoom.objects.select_for_update().filter(uid=uid, state=GameState.Joinable.value).first()

    def check_balance(user, room):
        if user.asset.balance < room.price:
            return RespCode.InvalidParams.value, _('余额不足')
        if user.asset.balance * 0.5 < room.price:
            return RespCode.InvalidParams.value, _('对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战。')
        return None

    # 检查用户权限
    permission_error = check_user_permissions(user)
    if permission_error:
        return permission_error

    with transaction.atomic():
        # 获取房间信息
        room = get_room(uid)
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')

        # 检查用户是否已经加入
        if CaseRoomBet.objects.filter(user=user, room=room).exists():
            return RespCode.InvalidParams.value, _('Already joined')

        # 检查用户余额
        balance_error = check_balance(user, room)
        if balance_error:
            return balance_error

        # 更新用户余额
        user.update_balance(-room.price, _('对战'))
        # 只记录普通用户
        if not user.is_staff:
            _logger.info('用户 {} 加入房间 {}'.format(user.steam.personaname, room.uid))

        # 创建参与记录
        bet = CaseRoomBet.objects.create(user=user, room=room)

        # 更新房间状态
        if room.bets.count() == room.max_joiner:
            room.state = GameState.Full.value
            # 只记录普通用户
            if not user.is_staff:
                _logger.info('对战人数已满，开始游戏: {}, 用户: {}'.format(room.uid, user.steam.personaname))
        room.save()

        # 发送房间更新信息
        room_data = CaseRoomSerializer(room).data
        # 房间满员时，通知前端开始倒计时
        if room.state == GameState.Full.value:
            room_data['countdown_start'] = True
        ws_send_box_room(room_data, 'update')


        # 返回响应数据
        data = {
            'bet': bet.uid
        }
        return RespCode.Succeed.value, data


def join_battle_case_room(user, uid, team):
    """
    加入PK房间
    :param uid: 房间uid
    :return:
    """
    if team not in [BetTeamType.CT.value, BetTeamType.T.value]:
        return RespCode.InvalidParams.value, _('Invalid room type')
    # user = AuthUser.objects.filter(username='76561199019852413').first()
    # user = AuthUser.objects.filter(username='76561199019195399').first()
    # user = AuthUser.objects.filter(username='76561199019856339').first()
    with transaction.atomic():
        last_room_count = CaseRoom.objects.filter(
            bets__user=user,
            state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value,
                GameState.Full.value,
                GameState.Running.value,
            ]).count()
        if last_room_count >= settings.CASE_ROOM_CREATE_MAX:
            return RespCode.InvalidParams.value, _('Over max room count')

        room = CaseRoom.objects.select_for_update().filter(uid=uid, state=GameState.Joinable.value).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')

        if CaseRoomBet.objects.filter(user=user, room=room).first():
            return RespCode.InvalidParams.value, _('Already joined')

        if CaseRoomBet.objects.filter(room=room, team=team).count() > 1:
            return RespCode.InvalidParams.value, _('Invalid team')

        user.update_balance(-room.price, _('对战'))
        #_logger.info('User {} join bettle case room {}'.format(user, room.uid))
        bet = CaseRoomBet.objects.create(user=user, room=room, team=team)
        if room.bets.count() == room.max_joiner:
            room.state = GameState.Full.value
            #_logger.info('Case room full: {}'.format(room.uid))
        room.save()

        room_data = CaseRoomSerializer(room).data
        # 房间满员时，通知前端开始倒计时
        if room.state == GameState.Full.value:
            room_data['countdown_start'] = True
        ws_send_box_room(room_data, 'update')
        
        # 获取用户在房间中的位置
        user_bet = CaseRoomBet.objects.filter(user=user, room=room).first()
        joiner_count = room.bets.count()
        
        resp = {
            'room_uid': room.uid,
            'short_id': room.short_id,
            'joined': True,
            'position': joiner_count,  # 用户在房间中的位置
            'joiner_count': joiner_count,
            'max_joiner': room.max_joiner,
            'state': room.state
        }
        return RespCode.Succeed.value, resp


def quit_case_room(user, uid):
    """
    普通参与者退出对战房间
    注意：房主不能使用此接口退出，需要使用dismiss接口解散房间
    """
    with transaction.atomic():
        room = CaseRoom.objects.select_for_update().filter(uid=uid, state=GameState.Joinable.value).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        bet = CaseRoomBet.objects.filter(user=user, room=room).first()
        if not bet:
            return RespCode.InvalidParams.value, _('Invalid bet')
        
        # 检查是否为房主
        if user == room.user:
            return RespCode.BusinessError.value, _('房主不能退出房间，请使用解散功能')
        
        # 普通参与者退出逻辑
        user.update_balance(room.price, _('Case room bet quit'))
        _logger.info('User {} quit case room {}'.format(user, room.uid))
        bet.delete()
        
        # 发送房间更新的WebSocket消息
        room_data = CaseRoomSerializer(room, fields=('uid', 'short_id', 'state', 'joiner_count', 'bets', 'update_time')).data
        ws_send_box_room(room_data, 'update')

        # 返回与文档一致的退款信息
        resp = {
            'success': True,
            'refund_amount': room.price
        }
        
        return RespCode.Succeed.value, resp


def dismiss_case_room(user, uid):
    """
    房主解散对战房间
    只有房主可以解散房间，解散后所有参与者退款
    """
    with transaction.atomic():
        room = CaseRoom.objects.select_for_update().filter(uid=uid, state=GameState.Joinable.value).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        
        # 检查是否为房主
        if user != room.user:
            return RespCode.BusinessError.value, _('只有房主可以解散房间')
        
        # 获取房间所有参与者
        bets = CaseRoomBet.objects.filter(room=room)
        
        # 解散房间逻辑：所有参与者退款
        refund_amount = 0
        participant_count = 0
        for bet in bets:
            bet_user = bet.user
            bet_user.update_balance(room.price, _('Case room dismissed by owner'))
            refund_amount += room.price
            participant_count += 1
            _logger.info('User {} refunded from dismissed room {}'.format(bet_user, room.uid))
            bet.delete()
        
        # 更新房间状态为已取消
        room.state = GameState.Cancelled.value
        room.save()
        
        # 发送房间取消的WebSocket消息
        room_data = CaseRoomSerializer(room, fields=('uid', 'short_id', 'state', 'update_time')).data
        ws_send_box_room(room_data, 'cancel')
        
        if participant_count > 1:
            _logger.info('Room {} dismissed by owner {}, {} participants refunded'.format(
                room.uid, user, participant_count))
        else:
            _logger.info('Empty room {} closed by owner {}'.format(room.uid, user))

        resp = {
            'room_uid': room.uid,
            'dismissed': True,
            'refund_amount': refund_amount,
            'participant_count': participant_count
        }
        
        return RespCode.Succeed.value, resp


def ws_send_room_data(room, action):
    sids = get_room_sids(room.short_id)
    bets = CaseRoomBet.objects.filter(room=room)
    bets_data = CaseRoomBetSerializer(bets, many=True).data
    # bets_data = json.dumps(bets_data)
    # print(action)
    for sid in sids:
        ws_send_box_room_detail(bets_data, action, sid)

    if action == 'end':
        delete_room(room.short_id)


def check_case_room():
    """
    检查并处理对战房间的定时任务
    新增分布式锁机制防止并发处理
    """
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            # 🔥 新增：使用分布式锁防止多进程同时处理房间
            lock_key = "case_room_processing_lock"
            lock_timeout = 30  # 30秒锁定时间
            
            # 尝试获取分布式锁
            if not acquire_distributed_lock(lock_key, lock_timeout):
                _logger.debug("Another process is handling case rooms, skipping this cycle")
                time.sleep(2)
                continue

            try:
                battle_rooms = CaseRoom.objects.filter(state__in=[
                    GameState.Full.value,
                    GameState.Running.value,  # 恢复对进行中房间的处理
                ], type=RoomType.Battle.value)
                
                processed_rooms = 0
                for room in battle_rooms:
                    # 🔥 新增：为每个房间获取独立锁
                    room_lock_key = f"room_processing:{room.uid}"
                    if acquire_distributed_lock(room_lock_key, 60):  # 60秒房间锁
                        try:
                            _logger.info(f"Processing room {room.short_id} (state={room.state}) with lock")
                            ready_to_run_room(room.uid)
                            processed_rooms += 1
                        finally:
                            release_distributed_lock(room_lock_key)
                    else:
                        _logger.info(f"Room {room.short_id} is being processed by another thread, skipping")

                # 临时启用团队对战处理，用于清理历史卡住的房间
                team_rooms = CaseRoom.objects.filter(state__in=[
                    GameState.Full.value,
                    GameState.Running.value,  # 恢复对进行中房间的处理
                ], type=RoomType.TeamBattle.value)
                
                for room in team_rooms:
                    room_lock_key = f"room_processing:{room.uid}"
                    if acquire_distributed_lock(room_lock_key, 60):
                        try:
                            ready_to_run_room(room.uid)
                            processed_rooms += 1
                        finally:
                            release_distributed_lock(room_lock_key)

                if processed_rooms > 0:
                    _logger.info(f"Processed {processed_rooms} rooms in this cycle")

            finally:
                # 释放主锁
                release_distributed_lock(lock_key)

            # 🔥 新增：检查并强制结束长时间运行的房间
            check_and_force_end_stuck_rooms()

            # 处理过期房间
            seconds = get_box_room_expire()
            expire_rooms = CaseRoom.objects.filter(state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value
            ], update_time__lt=(timezone.now() - timedelta(seconds=seconds)))

            for eroom in expire_rooms:
                room_lock_key = f"room_processing:{eroom.uid}"
                if acquire_distributed_lock(room_lock_key, 30):
                    try:
                        cancel_case_room(eroom.uid)
                    finally:
                        release_distributed_lock(room_lock_key)
                        
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(2)

def check_run_case_room():
    """
    执行对战房间的定时任务
    新增分布式锁机制防止并发处理
    """
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            # 🔥 新增：使用分布式锁防止多进程同时处理房间
            lock_key = "case_room_execution_lock"
            lock_timeout = 45  # 45秒锁定时间
            
            # 尝试获取分布式锁
            if not acquire_distributed_lock(lock_key, lock_timeout):
                _logger.debug("Another process is executing case rooms, skipping this cycle")
                time.sleep(8)
                continue

            try:
                battle_rooms = CaseRoom.objects.filter(state=GameState.Running.value, type=RoomType.Battle.value)
                
                executed_rooms = 0
                for room in battle_rooms:
                    # 🔥 新增：为每个房间获取独立锁
                    room_lock_key = f"room_execution:{room.uid}"
                    if acquire_distributed_lock(room_lock_key, 120):  # 120秒房间执行锁
                        try:
                            _logger.debug(f"Executing room {room.short_id} with lock")
                            run_battle_room(room.uid)
                            executed_rooms += 1
                        finally:
                            release_distributed_lock(room_lock_key)
                    else:
                        _logger.debug(f"Room {room.short_id} is being executed by another thread, skipping")

                # 临时启用团队对战处理，用于清理历史卡住的房间
                team_rooms = CaseRoom.objects.filter(state=GameState.Running.value, type=RoomType.TeamBattle.value)
                for room in team_rooms:
                    room_lock_key = f"room_execution:{room.uid}"
                    if acquire_distributed_lock(room_lock_key, 120):
                        try:
                            run_battle_room(room.uid)  # 使用run_battle_room处理团队对战
                            executed_rooms += 1
                        finally:
                            release_distributed_lock(room_lock_key)

                if executed_rooms > 0:
                    _logger.info(f"Executed {executed_rooms} rooms in this cycle")

            finally:
                # 释放主锁
                release_distributed_lock(lock_key)

        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(8)

def acquire_distributed_lock(lock_key, timeout_seconds, retry_times=3, retry_delay=0.1):
    """
    获取分布式锁，增强版本

    Args:
        lock_key: 锁的键名
        timeout_seconds: 锁的超时时间（秒）
        retry_times: 重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        bool: 是否成功获取锁
    """
    import time
    import os
    import uuid

    for attempt in range(retry_times):
        try:
            r = get_redis_connection()
            if not r:
                _logger.warning(f"Redis connection not available for lock {lock_key}")
                return False

            # 🔥 改进：使用UUID确保锁值的唯一性，防止误释放
            lock_value = f"{os.getpid()}:{uuid.uuid4().hex}:{int(time.time())}"

            # 使用Redis的SET NX EX命令实现分布式锁
            result = r.set(lock_key, lock_value, nx=True, ex=timeout_seconds)

            if result:
                _logger.debug(f"Acquired lock: {lock_key} (attempt {attempt + 1})")
                # 🔥 新增：将锁值存储到线程本地存储，用于安全释放
                import threading
                if not hasattr(threading.current_thread(), 'acquired_locks'):
                    threading.current_thread().acquired_locks = {}
                threading.current_thread().acquired_locks[lock_key] = lock_value
                return True
            else:
                if attempt < retry_times - 1:
                    _logger.debug(f"Lock {lock_key} busy, retrying in {retry_delay}s (attempt {attempt + 1}/{retry_times})")
                    time.sleep(retry_delay)
                else:
                    _logger.debug(f"Failed to acquire lock {lock_key} after {retry_times} attempts")
                return False

        except Exception as e:
            _logger.error(f"Error acquiring lock {lock_key} (attempt {attempt + 1}): {str(e)}")
            if attempt < retry_times - 1:
                time.sleep(retry_delay)

    return False

def release_distributed_lock(lock_key):
    """
    释放分布式锁，增强版本
    """
    try:
        import threading
        r = get_redis_connection()
        if not r:
            return False

        # 🔥 改进：检查是否是当前线程获取的锁
        current_thread = threading.current_thread()
        if hasattr(current_thread, 'acquired_locks') and lock_key in current_thread.acquired_locks:
            expected_value = current_thread.acquired_locks[lock_key]

            # 🔥 使用Lua脚本确保原子性释放锁
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """

            result = r.eval(lua_script, 1, lock_key, expected_value)

            if result:
                _logger.debug(f"Released lock: {lock_key}")
                del current_thread.acquired_locks[lock_key]
                return True
            else:
                _logger.warning(f"Lock {lock_key} was not owned by current thread or already expired")
                # 清理本地记录
                if lock_key in current_thread.acquired_locks:
                    del current_thread.acquired_locks[lock_key]
                return False
        else:
            # 降级方案：直接删除锁（不安全，但总比不释放好）
            result = r.delete(lock_key)
            _logger.warning(f"Force released lock {lock_key} (no local record): {bool(result)}")
            return bool(result)

    except Exception as e:
        _logger.error(f"Failed to release lock {lock_key}: {str(e)}")
        return False


def cleanup_expired_locks():
    """
    清理过期的锁（定期维护任务）
    """
    try:
        r = get_redis_connection()
        if not r:
            return

        # 获取所有锁相关的键
        lock_keys = r.keys("room_processing:*") + r.keys("room_execution:*") + r.keys("force_end_room:*")

        cleaned_count = 0
        for key in lock_keys:
            try:
                # 检查锁是否过期（通过TTL）
                ttl = r.ttl(key)
                if ttl == -1:  # 没有过期时间的锁
                    r.delete(key)
                    cleaned_count += 1
                    _logger.warning(f"Cleaned lock without TTL: {key}")
            except Exception as e:
                _logger.error(f"Error checking lock {key}: {e}")

        if cleaned_count > 0:
            _logger.info(f"Cleaned {cleaned_count} expired locks")

    except Exception as e:
        _logger.error(f"Error in cleanup_expired_locks: {e}")


class DistributedLockContext:
    """
    分布式锁上下文管理器，确保锁的正确释放
    """
    def __init__(self, lock_key, timeout_seconds=60, retry_times=3):
        self.lock_key = lock_key
        self.timeout_seconds = timeout_seconds
        self.retry_times = retry_times
        self.acquired = False

    def __enter__(self):
        self.acquired = acquire_distributed_lock(
            self.lock_key,
            self.timeout_seconds,
            self.retry_times
        )
        if not self.acquired:
            raise RuntimeError(f"Failed to acquire lock: {self.lock_key}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.acquired:
            release_distributed_lock(self.lock_key)
        return False  # 不抑制异常
        _logger.error(f"Failed to release lock {lock_key}: {str(e)}")
        return False

def ready_to_run_room(gid):
    try:
        #_logger.info('Ready To Run case room: {}'.format(gid))
        with transaction.atomic():
            room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
            if not room:
                return

            # 🔥 新增：检查房间状态，确保满员房间能正常推进
            if room.state == GameState.Full.value:
                bets_count = room.bets.count()
                if bets_count == room.max_joiner:
                    _logger.info(f'Room {room.short_id} is full ({bets_count}/{room.max_joiner}), proceeding with battle')
                elif bets_count < room.max_joiner:
                    _logger.warning(f'Room {room.short_id} marked as full but has {bets_count}/{room.max_joiner} participants')
                    # 如果人数不足，重新设置为可加入状态
                    room.state = GameState.Joinable.value
                    room.save()
                    _logger.info(f'Reset room {room.short_id} to joinable state')
                    return
            elif room.state == GameState.Running.value:
                _logger.info(f'Room {room.short_id} is already running, checking for next round')
            else:
                _logger.info(f'Room {room.short_id} state is {room.state}, checking if should process')

            room_round = CaseRoomRound.objects.filter(room=room, opened=False).order_by('create_time').first()
            if room_round:
                if room.state == GameState.Full.value:
                    room.state = GameState.Running.value
                room.save()
                
                # 🔥 修复：简化逻辑，优先使用稳定的传统模式
                async_available = False

                # 🔥 可选：尝试异步模式（如果可用且稳定）
                try:
                    from .async_battle_progression import AsyncBattleProgressionManager
                    from .compat_async import get_async_processor
                    import asyncio

                    # 只有在明确启用异步模式时才使用
                    use_async = getattr(settings, 'USE_ASYNC_BATTLE_PROGRESSION', False)
                    if use_async:
                        async_available = True
                        _logger.debug(f'Async battle progression enabled for room {room.short_id}')
                    else:
                        _logger.debug(f'Using traditional mode for room {room.short_id} (async disabled)')

                except ImportError as e:
                    _logger.debug(f'异步模块不可用，使用传统模式: {e}')
                    async_available = False

                if async_available:
                    try:
                        # 初始化异步对战推进管理器
                        progression_manager = AsyncBattleProgressionManager(room.uid)
                        total_rounds = CaseRoomRound.objects.filter(room=room).count()
                        async_processor = get_async_processor()

                        # 异步初始化和开始第一轮
                        async def start_battle_async():
                            await progression_manager.initialize_battle_progression(total_rounds)
                            await progression_manager.start_next_round()
                            # 启动消息处理
                            await progression_manager.process_message_queue()

                        # 使用兼容性异步处理器创建任务
                        async_processor.create_task_safe(start_battle_async())

                        # 发送对战开始通知（保持兼容性）
                        room_data = CaseRoomSerializer(room).data
                        ws_send_box_room(room_data, 'start')
                        _logger.info(f'Battle room {room.short_id} started with async progression system')

                    except Exception as async_error:
                        _logger.error(f'异步推进系统启动失败: room={room.short_id}, error={async_error}')
                        async_available = False

                # 如果异步不可用，使用传统模式
                if not async_available:
                    # 🔥 修复：降级到原始逻辑，实际启动第一轮开箱
                    _logger.info(f'Using traditional mode for room {room.short_id}')

                    # 发送对战开始通知
                    try:
                        room_data = CaseRoomSerializer(room).data
                        ws_send_box_room(room_data, 'start')
                    except Exception as ws_error:
                        _logger.warning(f'Failed to send start notification for room {room.short_id}: {ws_error}')

                    # 🔥 关键修复：实际启动第一轮开箱
                    try:
                        # 检查箱子配置
                        case = room_round.case
                        available_drops = case.drops.all()
                        if available_drops.count() > 0:
                            # 标记回合已开启，防止重复处理
                            room_round.opened = True
                            room_round.save()

                            # 启动第一轮开箱
                            open_room_case(room, room_round)
                            _logger.info(f'Battle room {room.short_id} first round started successfully')
                        else:
                            _logger.error(f'Case {case.case_key} in room {room.short_id} has no drops')
                            # 🔥 修复：即使箱子没有掉落物，也不要失败，而是跳过这轮
                            room_round.opened = True
                            room_round.save()
                            _logger.warning(f'Skipped round {room_round.id} due to no drops, marked as opened')

                            # 检查是否还有其他轮次
                            remaining_rounds = CaseRoomRound.objects.filter(room=room, opened=False).count()
                            if remaining_rounds == 0:
                                # 没有更多轮次，直接结束游戏
                                _logger.info(f'No more rounds for room {room.short_id}, ending game')
                                end_battle_room(room)

                    except Exception as fallback_error:
                        _logger.error(f'Traditional mode failed for room {room.short_id}: {fallback_error}')
                        # 🔥 修复：即使传统模式失败，也不要抛出异常，让房间保持状态等待下次处理
                        _logger.warning(f'Room {room.short_id} will be retried later')
            else:
                _logger.info(f'Room {room.short_id} has no unopened rounds, checking if should end')
                # 没有未开启的回合，检查是否应该结束游戏
                try:
                    end_battle_room(room)
                except Exception as end_error:
                    _logger.error(f'Failed to end room {room.short_id}: {end_error}')
                    # 即使结束失败，也不要抛出异常
    except Exception as e:
        _logger.exception(e)


def open_room_case(room, room_round):
    """
    优化版开箱函数，移除阻塞性等待，支持快速处理
    """
    case = room_round.case
    available_drops = case.drops.all()
    bets = CaseRoomBet.objects.filter(room=room)
    
    # 1. 准备参与者数据
    participants = prepare_participant_data(bets, room_round)
    
    # 2. 使用轮次管理器获取当前回合数 - 修复硬编码问题
    from .round_manager import BattleRoundManager
    round_manager = BattleRoundManager(room.uid)
    current_round = round_manager.get_current_round()
    total_rounds = round_manager.get_total_rounds()
    
    # 验证轮次合理性
    try:
        round_manager.validate_round(current_round)
    except ValueError as e:
        _logger.error(f'轮次验证失败: room={room.short_id}, error={e}')
        # 使用安全的回合数
        current_round = 1
    
    # 3. 发送回合开始消息
    ws_send_round_start(room.uid, current_round, total_rounds, participants)
    _logger.info(f'Round {current_round}/{total_rounds} started for room {room.short_id}')
    
    # 4. 生成动画ID并开始开箱动画（立即执行，无等待）
    animation_id = generate_animation_id()
    ws_send_opening_start(room.uid, animation_id, participants, round_number=current_round, total_rounds=total_rounds)
    _logger.info(f'Opening animation started for room {room.short_id}, animation_id: {animation_id}')
    
    # 5. 处理开箱逻辑（立即执行，无模拟等待）
    send_box_game_list = []
    try:
        for bet in bets:
            user = bet.user
            chance_type_name = 'drop_chance_{type}'.format(type=user.extra.box_chance_type)
            weights_array = [d.get(chance_type_name, 0) for d in DropItemSerializer(
                available_drops, many=True, fields=('drop_chance_a', 'drop_chance_b', 'drop_chance_c',
                                                    'drop_chance_d', 'drop_chance_e')).data]
            if sum(weights_array) <= 0:
                _logger.error(f'Case {case.case_key} has invalid drop chances for room {room.short_id}')
                raise ParamException(_('Case chance invalid.'))
            
            if len(available_drops) > 1:
                chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
            else:
                chosen_drops = available_drops
            
            if not chosen_drops:
                _logger.error(f'No drops chosen for case {case.case_key} in room {room.short_id}')
                raise ParamException(_('Case chance invalid.'))
            
            drop = chosen_drops[0]
            case_item_info = {
                'room': room,
                # 🔥 修复：移除不存在的round字段
                'bet': bet,
                'item_info': drop.item_info,
                'item_type': drop.item_type,
                'price': get_drop_price(drop)
            }
            CaseRoomItem.objects.create(**case_item_info)
            
            # open_amount 可能为 None，先转为 0
            if bet.open_amount is None:
                bet.open_amount = 0
            bet.open_amount += case_item_info['price']
            bet.save()
            _logger.info('User {} open item {} in bet {}'.format(user, drop.item_info, bet))

            record = CaseRecord.objects.create(user=user, case=case, item_info=drop.item_info,
                                               price=get_drop_price(drop), source=room.type)
            record_data = CaseRecordSerializer(record).data
            record_data['round'] = current_round  # 供前端 add_record/new 消息直接读取当前回合
            send_box_game_list.append(record_data)
    except Exception as e:
        _logger.error(f'Error during case opening for room {room.short_id}: {e}')
        raise

    # 6. 发送回合结果（立即发送）
    results = prepare_round_results(bets, animation_id, room_round)
    ws_send_round_result(room.uid, animation_id, results, round_number=current_round, total_rounds=total_rounds)
    _logger.info(f'Round {current_round}/{total_rounds} results sent for room {room.short_id}')
    
    # 7. 发送传统消息保持兼容性
    ws_send_room_data(room, 'round')
    for game_data in send_box_game_list:
        ws_send_box_game(game_data, 'new')
    
    # 8. 🚀 异步推进轮次处理
    room_round.opened = True
    room_round.save()
    
    # 🚀 使用增强的异步处理系统进行轮次推进
    try:
        from .enhanced_async_system import get_battle_async_processor
        battle_processor = get_battle_async_processor()

        # 准备轮次完成数据
        round_data = {
            'room_uid': room.uid,
            'room_round_id': room_round.id,
            'current_round': current_round,
            'total_rounds': total_rounds,
            'bets_count': len(bets),
            'room_short_id': room.short_id,
            'timestamp': timezone.now().isoformat()
        }

        # 提交异步任务
        task_id = battle_processor.submit_round_completion(
            room.uid, round_data, priority=3
        )

        if task_id:
            _logger.info(f'增强异步轮次推进已启动: room={room.short_id}, round={current_round}, task={task_id}')
        else:
            _logger.warning(f'异步任务提交失败，继续同步处理: room={room.short_id}')

    except Exception as async_error:
        _logger.error(f'增强异步轮次推进失败: room={room.short_id}, error={async_error}')
        # 🔥 修复：异步失败时使用同步降级，确保对战能继续推进
        _logger.info(f'异步失败，使用同步降级推进: room={room.short_id}')

        try:
            # 延迟2秒后同步推进下一轮
            import threading
            import time

            def delayed_sync_progression():
                time.sleep(2)  # 等待动画完成
                try:
                    _logger.info(f'同步降级推进下一轮: room={room.short_id}')
                    run_battle_room(room.uid)
                except Exception as sync_error:
                    _logger.error(f'同步降级也失败: room={room.short_id}, error={sync_error}')

            # 启动后台线程进行同步推进
            thread = threading.Thread(target=delayed_sync_progression, daemon=True)
            thread.start()

        except Exception as fallback_error:
            _logger.error(f'降级机制失败: room={room.short_id}, error={fallback_error}')
    
    _logger.info(f'Round {current_round}/{total_rounds} completed for room {room.short_id}')


def send_free_item(room, bets):    

    # 查询所有启用的 GiveawayItems 关联的 ItemInfo 实例
    enabled_giveaway_items = GiveawayItems.objects.filter(enable=True)

    # 获取这些 GiveawayItems 对应的 ItemInfo 实例的 ID 列表
    item_info_ids = enabled_giveaway_items.values_list('item_info', flat=True)

    # 使用 ItemInfo 的 ID 列表来获取对应的 ItemInfo 实例
    free_items = ItemInfo.objects.filter(id__in=item_info_ids)


    if len(free_items) > 0:
        free_item = random.choices(free_items)[0]
        bets = bets.filter(victory=0).all()
        for bet in bets:
            bet.win_amount = get_item_price_by_id(free_item.id).price
            bet.save()
            data = {
                'room': room,
                'bet': None,  # 赠送饰品不应该关联到bet的open_items
                'winner': bet,
                'item_info': free_item,
                'item_type': 2,  # free
                'price': get_item_price_by_id(free_item.id).price
            }
            CaseRoomItem.objects.create(**data)
            PackageItem.objects.create(user=bet.user, item_info=free_item, assetid='0',
                                       source=PackageSourceType.BattleRoom.value,
                                       instanceid='0', state=PackageState.Available.value,
                                       amount=get_item_price_by_id(free_item.id).price,  case_name="对战")


def run_battle_room(gid):
    """
    执行对战房间逻辑，增强异常处理和状态管理
    """
    # 🔥 新增：获取房间级别的分布式锁，防止并发处理同一房间
    room_lock_key = f"battle_room_processing:{gid}"
    if not acquire_distributed_lock(room_lock_key, 300):  # 5分钟锁定时间
        _logger.debug(f'Room {gid} is being processed by another thread, skipping')
        return

    try:
        with transaction.atomic():
            room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
            if not room:
                _logger.warning(f'Room {gid} not found, skipping')
                return

            # 🔥 新增：检查房间是否已经处理过
            if room.state not in [GameState.Full.value, GameState.Running.value]:
                _logger.info(f'Room {room.short_id} state is {room.state}, skipping processing')
                return

            # 🔥 新增：检查房间是否超时（超过1小时的房间强制结束）
            from django.utils import timezone
            from datetime import timedelta
            if room.update_time < timezone.now() - timedelta(hours=1):
                _logger.warning(f'Room {room.short_id} has been running for over 1 hour, forcing end')
                force_end_stuck_room(room)
                return

            _logger.info(f'run_battle_room: Processing room {room.short_id} (state={room.state})')

            # 🔥 改进：使用select_for_update确保并发安全
            room_round = CaseRoomRound.objects.select_for_update().filter(
                room=room,
                opened=False
            ).order_by('create_time').first()

            if room_round:
                # 🔥 新增：双重检查，确保回合确实未开启
                if room_round.opened:
                    _logger.warning(f'Room {room.short_id} round {room_round.id} already opened, skipping')
                    return

                # 更新房间状态
                if room.state == GameState.Full.value:
                    room.state = GameState.Running.value
                    room.save()

                # 🔥 关键：先标记回合已开启，防止重复处理
                room_round.opened = True
                room_round.save()

                _logger.info(f'Processing room {room.short_id}, round {room_round.id}')

                case = room_round.case
                available_drops = case.drops.all()
                if available_drops.count() > 0:
                    try:
                        open_room_case(room, room_round)
                    except Exception as e:
                        _logger.error(f'Error opening case for room {room.short_id}, round {room_round.id}: {e}')
                        # 🔥 修复：开箱失败时不要卡住，继续下一轮或结束游戏
                        remaining_rounds = CaseRoomRound.objects.filter(room=room, opened=False).count()
                        if remaining_rounds == 0:
                            # 没有更多轮次，强制结束游戏
                            force_end_stuck_room(room)
                        return
                else:
                    _logger.error(f'Case {case.case_key} in room {room.short_id} has no drops')
                    # 🔥 修复：跳过这个轮次而不是取消整个房间
                    _logger.warning(f'Skipped round {room_round.id} due to no drops, continuing with next round')
                    # 检查是否还有其他轮次
                    remaining_rounds = CaseRoomRound.objects.filter(room=room, opened=False).count()
                    if remaining_rounds == 0:
                        # 没有更多轮次，强制结束游戏
                        force_end_stuck_room(room)
                    return
            else:
                # 没有未开启的回合，处理对战结束逻辑
                try:
                    end_battle_room(room)
                except Exception as e:
                    _logger.error(f'Error ending battle room {room.short_id}: {e}')
                    # 🔥 修复：结束游戏失败时强制结束
                    force_end_stuck_room(room)

    except Exception as e:
        _logger.exception(f'Critical error in run_battle_room for {gid}: {e}')
        # 🔥 修复：发生严重错误时，尝试强制结束房间
        try:
            room = CaseRoom.objects.filter(uid=gid).first()
            if room and room.state in [GameState.Full.value, GameState.Running.value]:
                force_end_stuck_room(room)
        except Exception as cleanup_error:
            _logger.error(f'Failed to cleanup room {gid} after error: {cleanup_error}')
    finally:
        # 🔥 关键：确保锁被释放
        release_distributed_lock(room_lock_key)


def end_battle_room(room):
    """
    结束对战房间的逻辑，从run_battle_room中分离出来
    """
    bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')

    # 检查参与者数量
    if bets.count() == 0:
        _logger.warning(f'Room {room.short_id} has no participants, cancelling room')
        room.state = GameState.Cancelled.value
        room.save()
        return
    elif bets.count() < room.max_joiner:
        _logger.info(f'Room {room.short_id} has {bets.count()}/{room.max_joiner} participants, allowing to end')

    # 🔥 修复：检查房间是否已经结束，但允许重新分配物品
    if room.state == GameState.End.value:
        # 检查是否已经有获胜者和物品分配
        winner_exists = bets.filter(victory=1).exists()
        items_assigned = CaseRoomItem.objects.filter(room=room, winner__isnull=False).exists()

        if winner_exists and items_assigned:
            _logger.info(f'Room {room.short_id} already ended with winner and items assigned, skipping')
            return
        else:
            _logger.info(f'Room {room.short_id} ended but missing winner/items, re-processing')
            # 继续执行物品分配逻辑

    # 先将所有 bet 的 victory 设为 0，再把 winner 设为 1
    for b in bets:
        b.victory = 0
        if b.win_amount is None:
            b.win_amount = 0
        if b.win_items_count is None:
            b.win_items_count = 0
        b.save()

    winner = bets[0]
    # 确保至少有2个参与者才比较分数
    if len(bets) >= 2 and bets[0].open_amount == bets[1].open_amount:
        win_index = random.choice([0, 1])
        winner = bets[win_index]

    # 🔥 关键：先更新房间状态，防止重复处理
    if room.state == GameState.Running.value:
        room.state = GameState.End.value
        room.save()

        _logger.info(f'Room {room.short_id} ended, winner: {winner.user.username}')

    winner.victory = 1
    # 若初始为 None，先设为 0，避免后续 += 报错
    if winner.win_amount is None:
        winner.win_amount = 0

    # 🔥 补充完整的对战结束逻辑
    if winner.win_items_count is None:
        winner.win_items_count = 0

    # 🔥 修复：处理获胜者的战利品分配
    pids = []

    # 将所有开箱物品分配给获胜者
    for bet in bets:
        items = CaseRoomItem.objects.filter(bet=bet, item_type=1).order_by('price')
        for item in items:
            if not item.winner:
                item.winner = winner
                item.save()
                winner.win_amount += item.price
                winner.win_items_count += 1
                _logger.info(f'User {winner.user.username} wins item {item.item_info} from bet {bet.user.username}')

    winner.save()

    # 为获胜者创建PackageItem并收集PID
    win_items = CaseRoomItem.objects.filter(winner=winner, item_type=1)
    for item in win_items:
        package = PackageItem.objects.create(
            user=winner.user,
            item_info=item.item_info,
            assetid='0',
            source=PackageSourceType.BattleRoom.value,
            instanceid='0',
            state=PackageState.Available.value,
            amount=item.price,
            case_name="对战"
        )
        pids.append(package.uid)

    # 发送对战结束消息
    winner_data = {
        'user': {
            'uid': winner.user.uid,
            'profile': {
                'nickname': winner.user.profile.nickname if hasattr(winner.user, 'profile') and winner.user.profile else winner.user.username,
                'avatar': safe_image_url(winner.user.profile.avatar) if hasattr(winner.user, 'profile') and winner.user.profile else ''
            }
        },
        'total_amount': float(winner.win_amount or 0),
        'victory': 1
    }

    final_results = []
    for bet in bets:
        final_results.append({
            'user': {
                'uid': bet.user.uid,
                'profile': {
                    'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') and bet.user.profile else bet.user.username,
                    'avatar': safe_image_url(bet.user.profile.avatar) if hasattr(bet.user, 'profile') and bet.user.profile else ''
                }
            },
            'open_amount': float(bet.open_amount or 0),
            'win_amount': float(bet.win_amount or 0),
            'victory': bet.victory,
            'total_items': bet.win_items_count or 0
        })

    # 发送对战结束消息
    try:
        ws_send_room_data(room, 'end')

        # 发送房间更新通知
        from .serializers import CaseRoomSerializer
        room_data = CaseRoomSerializer(room).data
        ws_send_box_room(room_data, 'update')

        # 缓存快速出售PID
        if pids:
            cache_quick_sell_pid(room.uid, winner.user.username, pids)

    except Exception as e:
        _logger.warning(f'Failed to send end notifications for room {room.short_id}: {e}')


def force_end_stuck_room(room):
    """
    强制结束卡住的房间
    """
    try:
        with transaction.atomic():
            # 重新获取房间确保数据最新
            room = CaseRoom.objects.select_for_update().get(uid=room.uid)

            if room.state in [GameState.End.value, GameState.Cancelled.value]:
                _logger.info(f'Room {room.short_id} already ended/cancelled, no need to force end')
                return

            bets = CaseRoomBet.objects.filter(room=room)

            if bets.count() == 0:
                # 没有参与者，直接取消
                room.state = GameState.Cancelled.value
                room.save()
                _logger.info(f'Force cancelled room {room.short_id} (no participants)')
            else:
                # 有参与者，强制结束并选择获胜者
                from django.db.models import Sum
                import random

                # 计算总金额
                bets_amount_qs = bets.aggregate(Sum('open_amount'))
                bets_amount = bets_amount_qs.get('open_amount__sum', 0) or 0

                # 按开箱金额排序确定获胜者
                sorted_bets = bets.order_by('-open_amount')
                winner = sorted_bets[0]

                # 如果有平局，随机选择获胜者
                if len(sorted_bets) >= 2 and sorted_bets[0].open_amount == sorted_bets[1].open_amount:
                    tied_bets = [bet for bet in sorted_bets if bet.open_amount == sorted_bets[0].open_amount]
                    winner = random.choice(tied_bets)

                # 重置所有参与者状态
                for bet in bets:
                    bet.victory = 0
                    if bet.win_amount is None:
                        bet.win_amount = 0
                    if bet.win_items_count is None:
                        bet.win_items_count = 0
                    bet.save()

                # 设置获胜者
                winner.victory = 1
                winner.win_amount += bets_amount  # 获胜者获得所有金额
                winner.save()

                # 更新房间状态
                room.state = GameState.End.value
                room.save()

                _logger.info(f'Force ended room {room.short_id}, winner: {winner.user.username}')

                # 发送结束通知
                try:
                    from .serializers import CaseRoomSerializer
                    room_data = CaseRoomSerializer(room).data
                    ws_send_box_room(room_data, 'update')
                    ws_send_room_data(room, 'end')
                except Exception as e:
                    _logger.warning(f'Failed to send end notification for room {room.short_id}: {e}')

    except Exception as e:
        _logger.error(f'Failed to force end room {room.short_id}: {e}')
        # 最后的降级方案：直接设置为取消状态
        try:
            room.state = GameState.Cancelled.value
            room.save()
            _logger.warning(f'Fallback: cancelled room {room.short_id}')
        except Exception as fallback_error:
            _logger.error(f'Even fallback failed for room {room.short_id}: {fallback_error}')


def check_and_force_end_stuck_rooms():
    """
    检查并强制结束卡住的房间
    """
    try:
        # 🔥 检查超过1小时的运行中房间
        one_hour_ago = timezone.now() - timedelta(hours=1)
        stuck_rooms = CaseRoom.objects.filter(
            state__in=[GameState.Full.value, GameState.Running.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=one_hour_ago
        )

        if stuck_rooms.count() > 0:
            _logger.warning(f'Found {stuck_rooms.count()} rooms stuck for over 1 hour')

            for room in stuck_rooms:
                room_lock_key = f"force_end_room:{room.uid}"
                if acquire_distributed_lock(room_lock_key, 60):
                    try:
                        _logger.info(f'Force ending stuck room {room.short_id} (running for over 1 hour)')
                        force_end_stuck_room(room)
                    except Exception as e:
                        _logger.error(f'Failed to force end stuck room {room.short_id}: {e}')
                    finally:
                        release_distributed_lock(room_lock_key)

        # 🔥 检查超过30分钟但没有轮次进展的房间
        thirty_min_ago = timezone.now() - timedelta(minutes=30)
        potentially_stuck = CaseRoom.objects.filter(
            state=GameState.Running.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=thirty_min_ago
        )

        for room in potentially_stuck:
            # 检查是否有未开启的轮次但长时间没有进展
            unopened_rounds = CaseRoomRound.objects.filter(room=room, opened=False).count()
            if unopened_rounds > 0:
                room_lock_key = f"check_stuck_room:{room.uid}"
                if acquire_distributed_lock(room_lock_key, 30):
                    try:
                        _logger.warning(f'Room {room.short_id} has {unopened_rounds} unopened rounds but no progress for 30+ minutes')
                        # 尝试推进房间
                        ready_to_run_room(room.uid)
                    except Exception as e:
                        _logger.error(f'Failed to push stuck room {room.short_id}: {e}')
                        # 如果推进失败，强制结束
                        force_end_stuck_room(room)
                    finally:
                        release_distributed_lock(room_lock_key)

        # 🔥 检查异常状态的房间（状态不一致）
        check_inconsistent_room_states()

    except Exception as e:
        _logger.error(f'Error in check_and_force_end_stuck_rooms: {e}')


def check_inconsistent_room_states():
    """
    检查状态不一致的房间
    """
    try:
        # 检查状态为Running但所有轮次都已开启的房间
        running_rooms = CaseRoom.objects.filter(
            state=GameState.Running.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        )

        for room in running_rooms:
            unopened_rounds = CaseRoomRound.objects.filter(room=room, opened=False).count()
            if unopened_rounds == 0:
                # 所有轮次都已开启，但房间仍在运行状态
                _logger.warning(f'Room {room.short_id} is in Running state but has no unopened rounds')
                room_lock_key = f"fix_inconsistent_room:{room.uid}"
                if acquire_distributed_lock(room_lock_key, 30):
                    try:
                        # 尝试正常结束房间
                        end_battle_room(room)
                    except Exception as e:
                        _logger.error(f'Failed to end inconsistent room {room.short_id}: {e}')
                        # 强制结束
                        force_end_stuck_room(room)
                    finally:
                        release_distributed_lock(room_lock_key)

        # 检查状态为Full但长时间没有开始的房间
        fifteen_min_ago = timezone.now() - timedelta(minutes=15)
        full_rooms = CaseRoom.objects.filter(
            state=GameState.Full.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=fifteen_min_ago
        )

        for room in full_rooms:
            _logger.warning(f'Room {room.short_id} has been Full for over 15 minutes without starting')
            room_lock_key = f"start_delayed_room:{room.uid}"
            if acquire_distributed_lock(room_lock_key, 30):
                try:
                    # 🔥 修复：检查房间是否真的满员
                    bets_count = room.bets.count()
                    if bets_count >= room.max_joiner:
                        _logger.info(f'Room {room.short_id} is full ({bets_count}/{room.max_joiner}), attempting to start')
                        # 尝试开始房间
                        ready_to_run_room(room.uid)
                    else:
                        _logger.warning(f'Room {room.short_id} marked as Full but only has {bets_count}/{room.max_joiner} participants, resetting to Joinable')
                        # 人数不足，重新设置为可加入状态
                        room.state = GameState.Joinable.value
                        room.save()
                except Exception as e:
                    _logger.error(f'Failed to start delayed room {room.short_id}: {e}')
                    # 🔥 修复：不要轻易取消满员房间，而是重新检查状态
                    bets_count = room.bets.count()
                    if bets_count >= room.max_joiner:
                        _logger.warning(f'Room {room.short_id} start failed but is full, keeping Full state for retry')
                        # 满员房间启动失败，保持Full状态等待下次重试
                    else:
                        _logger.warning(f'Room {room.short_id} start failed and not full, cancelling')
                        # 只有非满员房间才取消
                        room.state = GameState.Cancelled.value
                        room.save()
                finally:
                    release_distributed_lock(room_lock_key)

    except Exception as e:
        _logger.error(f'Error in check_inconsistent_room_states: {e}')


def run_incr_cache_for_bet(gid):
    room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
    bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
    for bet in bets:
        user = AuthUser.objects.filter(case_room_bets=bet).first()
        user_win = CaseRoomBet.objects.filter(user=user, victory=1).count()
        cache.set("{}:user_win".format(user), user_win)
        user_lose = CaseRoomBet.objects.filter(user=user, victory=0).count()
        cache.set("{}:user_lose".format(user), user_lose)


def create_equality_package(bets, create_package_list):
    """
    create item to user package
    """
    user_pids = {}
    for i in range(0, len(create_package_list)):
        _sum = 0
        pkg = create_package_list[i]
        pids = []
        for item in pkg:
            item.bet = bets[i]
            item.winner = bets[i]
            item.save()
            _sum += item.price
            package = PackageItem.objects.create(user=bets[i].user, item_info=item.item_info, assetid='0',
                                                 instanceid='0', state=PackageState.Available.value,
                                                 part=item.part, amount=item.price,
                                                 source=PackageSourceType.BattleRoom.value, case_name="对战")
            pids.append(package.uid)
        user_pids[bets[i].user.username] = pids
        # print(bets[i], _sum)
    return user_pids


def get_wait_split_list(create_package_list, avg_amount):
    """
    return [ user1_itemA, user2_itemA, user2_itemB]
    """
    wait_split = []
    for index in range(0, len(create_package_list)):
        bet_sum = sum(i.price for i in create_package_list[index])
        d = bet_sum - avg_amount
        # print('sum ', bet_sum, 'avg', avg_amount, index, 'd', d)
        if d < 0:
            continue
        i = 0
        sub_sum = 0
        while sub_sum < d:
            item = create_package_list[index][i]
            sub_sum += item.price
            wait_split.append(item)
            i += 1

        for j in range(0, i):
            b = create_package_list[index][j]
            b.split = True
            b.save()
    return wait_split


def get_create_package_list(bets, _filter=None):
    """
    return [ [user1_itemA, user1_itemB], [user2_itemA, user2_itemB]]
    """
    create_package_list = []
    for index in range(0, len(bets)):
        if _filter is None:
            qs_items = bets[index].open_items.all()
        else:
            qs_items = bets[index].open_items.filter(split=_filter)
        bets_list_pkg = []
        for item in qs_items:
            bets_list_pkg.append(item)
        create_package_list.append(bets_list_pkg)
    return create_package_list


def start_split(create_package_list, wait_split, avg_amount):
    """
    split wait_split list to create_package_list
    return create_package_list
    """
    for index in range(0, len(create_package_list)):
        bet_items_sum = sum([i.price for i in create_package_list[index]])
        d = round(avg_amount - bet_items_sum, 2)
        # print(avg_amount, d)
        if d <= 0:
            continue

        sub_list = []
        i = 0
        while d > 0:
            if len(wait_split) == 0 or i >= len(wait_split):
                break
            item = wait_split[i]
            _item_info = {
                'room': item.room,
                'round': item.round,
                'bet': item.bet,
                'item_info': item.item_info,
                'item_type': item.item_type,
            }
            if item.price < d:
                _item_info['price'] = round(item.price, 2)  # 不够到平均数
                _item = CaseRoomItem.objects.create(**_item_info)
                sub_list.append(item)
                d = d - item.price
            else:
                _item_info['price'] = d  # 够到平均数
                _item_info['bet'] = item.bet
                _item_info['part'] = True
                _item = CaseRoomItem.objects.create(**_item_info)
                sub_list.append(_item)

                item.price = round(item.price - d, 2)
                item.save()
                d = 0
            i += 1

        create_package_list[index] += sub_list
        # print('bets_list_pkg', index, sum([i.price for i in create_package_list[index]]))
        if i > 0:
            del wait_split[:i - 1]
    return create_package_list


def run_equality_room(gid):
    #_logger.info('Run case room: {}'.format(gid))
    with transaction.atomic():
        room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
        if not room:
            return

        room_round = CaseRoomRound.objects.filter(room=room, opened=False).order_by('create_time').first()
        if room_round:
            if room.state == GameState.Full.value:
                room.state = GameState.Running.value
                room.save()
            room_round.opened = True
            room_round.save()

            case = room_round.case
            available_drops = case.drops.all()
            if available_drops:
                open_room_case(room, room_round)
            else:
                raise ParamException(_('Case drops invalid.'))
        else:
            bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
            bets_amount_qs = CaseRoomBet.objects.filter(room=room).aggregate(Sum('open_amount'))
            bets_amount = bets_amount_qs.get('open_amount__sum', 0) or 0
            avg_amount = int(bets_amount / len(bets) * 100) / 100
            # print(bets, bets_amount, avg_amount)

            create_package_list = get_create_package_list(bets)
            wait_split = get_wait_split_list(create_package_list, avg_amount)
            create_package_list = get_create_package_list(bets, False)
            create_package_list = start_split(create_package_list, wait_split, avg_amount)

            user_pids = create_equality_package(bets, create_package_list)
            for k, v in user_pids.items():
                cache_quick_sell_pid(room.uid, k, v)
            bets.update(win_amount=avg_amount)
            bets.update(victory=1)

            #_logger.info('End case room {}'.format(room))
            if room.state == GameState.Running.value:
                room.state = GameState.End.value
                room.save()
            ws_send_room_data(room, 'end')

        room_data = CaseRoomSerializer(room).data
        ws_send_box_room(room_data, 'update')
    # run_incr_cache_for_bet(gid)


def get_team_list(ct_bets, t_bets):
    """
    reutrn team list [0, ct_package_list, t_package_list]
    """
    ct_package_list = []
    t_package_list = []
    for index in range(0, 2):
        ct_items = ct_bets[index].open_items.all()
        ct_list_pkg = []
        for item in ct_items:
            ct_list_pkg.append(item)
        ct_package_list.append(ct_list_pkg)

        t_items = t_bets[index].open_items.all()
        t_list_pkg = []
        for item in t_items:
            t_list_pkg.append(item)
        t_package_list.append(t_list_pkg)
    team_list = [0, ct_package_list, t_package_list]
    return team_list


def team_battle_res(ct_bets, t_bets):
    """
    return win_team, lose_team, win_team_bets, lose_team_bets
    """
    ct_amount = ct_bets.aggregate(Sum('open_amount')).get('open_amount__sum', 0) or 0
    t_amount = t_bets.aggregate(Sum('open_amount')).get('open_amount__sum', 0) or 0
    if ct_amount > t_amount:
        lose_team = BetTeamType.T.value
    elif ct_amount < t_amount:
        lose_team = BetTeamType.CT.value
    else:
        lose_team = random.choice([1, 2])
    if lose_team == BetTeamType.CT.value:
        win_team = BetTeamType.T.value
        win_team_bets = t_bets
        lose_team_bets = ct_bets
    else:
        win_team = BetTeamType.CT.value
        win_team_bets = ct_bets
        lose_team_bets = t_bets
    return win_team, lose_team, win_team_bets, lose_team_bets


def run_team_room(gid):
    #_logger.info('Run case room: {}'.format(gid))
    with transaction.atomic():
        room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
        if not room:
            return

        room_round = CaseRoomRound.objects.filter(room=room, opened=False).order_by('create_time').first()
        if room_round:
            if room.state == GameState.Full.value:
                room.state = GameState.Running.value
                room.save()
            room_round.opened = True
            room_round.save()

            case = room_round.case
            available_drops = case.drops.all()
            if available_drops:
                open_room_case(room, room_round)
            else:
                raise ParamException(_('Case drops invalid.'))
        else:
            bets_amount_qs = CaseRoomBet.objects.filter(room=room).aggregate(Sum('open_amount'))
            bets_amount = bets_amount_qs.get('open_amount__sum', 0) or 0
            avg_amount = int(bets_amount / 2 * 100) / 100

            ct_bets = CaseRoomBet.objects.filter(room=room, team=BetTeamType.CT.value).order_by('-open_amount')
            t_bets = CaseRoomBet.objects.filter(room=room, team=BetTeamType.T.value).order_by('-open_amount')
            # print(ct_bets, t_bets, bets_amount, avg_amount)

            team_list = get_team_list(ct_bets, t_bets)
            win_team, lose_team, win_team_bets, lose_team_bets = team_battle_res(ct_bets, t_bets)

            wait_split = []
            for item in team_list[lose_team][0]:
                wait_split.append(item)
            for item in team_list[lose_team][1]:
                wait_split.append(item)
            create_package_list = team_list[win_team]
            wait_split += get_wait_split_list(create_package_list, avg_amount)

            create_package_list = get_create_package_list(win_team_bets, False)
            create_package_list = start_split(create_package_list, wait_split, avg_amount)
            user_pids = create_equality_package(win_team_bets, create_package_list)
            for k, v in user_pids.items():
                cache_quick_sell_pid(room.uid, k, v)

            #_logger.info('End case room {}'.format(room))
            if room.state == GameState.Running.value:
                room.state = GameState.End.value
                room.save()

            win_team_bets.update(win_amount=avg_amount)
            win_team_bets.update(victory=1)
            send_free_item(room, lose_team_bets)
            ws_send_room_data(room, 'end')

        room_data = CaseRoomSerializer(room).data
        ws_send_box_room(room_data, 'update')
    # run_incr_cache_for_bet(gid)


def cancel_case_room(gid):
    #_logger.info('Cancel case room: {}'.format(gid))
    with transaction.atomic():
        room = CaseRoom.objects.select_for_update().filter(uid=gid).first()
        if not room:
            return

        room.state = GameState.Cancelled.value
        room.save()
        delete_room(room.short_id)

        bets = CaseRoomBet.objects.filter(room=room)
        for bet in bets:
            user = bet.user
            user.update_balance(room.price, _('Case room cancel'))


def get_room_list(user, query, fields, page, page_size):
    queryset = CaseRoom.objects.filter(state__in=[
        GameState.Joinable.value,
        GameState.Joining.value,
        GameState.Full.value,
        GameState.Running.value,
        GameState.End.value
    ]).filter(**query).order_by('state', '-create_time')
    filter_qs = queryset
    for qs in queryset:
        if qs.private and qs.user != user:
            filter_qs = filter_qs.exclude(uid=qs.uid)

    paginator = Paginator(filter_qs, page_size)
    rooms = paginator.page(page)
    # rooms_data = CaseRoomCacheSerializer(rooms, many=True, fields=fields).data
    rooms_data = CaseRoomSerializer(rooms, many=True, fields=fields).data

    resp = {
        'rooms': rooms_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_room_self(user, query, fields, page, page_size):
    queryset = CaseRoom.objects.filter(state__in=[
        GameState.Joinable.value,
        GameState.Joining.value,
        GameState.Full.value,
        GameState.Running.value
    ]).filter(user=user).order_by('state', '-create_time')

    paginator = Paginator(queryset, page_size)
    rooms = paginator.page(page)
    rooms_data = CaseRoomSerializer(rooms, many=True, fields=fields).data

    resp = {
        'rooms': rooms_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_room_detail(uid, sid, fields):
    if not uid:
        return RespCode.InvalidParams.value, {}
    else:
        room = CaseRoom.objects.filter(uid=uid).first()
    resp = {}
    if room:
        set_room_sid(room.short_id, sid)
        room_data = CaseRoomDetailSerializer(room, fields=fields).data
        resp = room_data
        get_room_sids(room.short_id)
    return RespCode.Succeed.value, resp


def leave_room_detail(user, query, sid):
    if not query:
        return RespCode.InvalidParams.value, {}
    else:
        room = CaseRoom.objects.filter(**query).first()
    resp = {}
    if room:
        remove_sid(room.short_id, sid)
        resp = {}
    return RespCode.Succeed.value, resp


def get_case_room_list():
    end_rooms = CaseRoom.objects.filter(state=GameState.End.value).order_by('-create_time')[:10]
    current_rooms = CaseRoom.objects.filter(state__in=[
        GameState.Joinable.value,
        GameState.Joining.value,
        GameState.Full.value,
        GameState.Running.value
    ]).order_by('-state', 'create_time')
    rooms = chain(end_rooms, current_rooms)
    rooms_data = CaseRoomSerializer(rooms, many=True).data
    return rooms_data


def quick_sell_room_item(user, uid):
    r = get_redis()
    key = QUICK_SELL_KEY.format(uid, user.username)
    pids = r.get(key)
    if pids:
        pids = pids.decode('utf-8')
        pids = json.loads(pids)
        code, resp = user_exchange_items(user, pids)
        if code == RespCode.Succeed.value:
            # 获取用户当前余额
            user.refresh_from_db()
            return code, {
                'item_uid': uid,
                'sold_price': resp.get('price', 0),
                'user_balance': user.asset.balance
            }
        return code, {}
    else:
        return RespCode.Succeed.value, {'ReturnPackage': 1}


def get_room_statistics(user):
    # 获取用户创建的房间
    rooms = CaseRoom.objects.filter(user=user)
    total_battles = rooms.count()
    
    # 获取用户参与的所有对战记录
    user_bets = CaseRoomBet.objects.filter(user=user)
    total_battles = user_bets.count()
    
    # 统计胜负
    win_bets = user_bets.filter(victory=1)
    total_wins = win_bets.count()
    total_losses = total_battles - total_wins
    
    # 计算胜率
    win_rate = round(total_wins / total_battles, 4) if total_battles > 0 else 0
    
    # 计算投入和收益
    total_invested = user_bets.aggregate(total=Sum('open_amount'))['total'] or 0
    total_earned = user_bets.aggregate(total=Sum('win_amount'))['total'] or 0
    net_profit = total_earned - total_invested
    
    # 最大单次胜利和损失
    biggest_win_bet = win_bets.order_by('-win_amount').first()
    biggest_win = biggest_win_bet.win_amount if biggest_win_bet else 0
    
    lose_bets = user_bets.filter(victory=0)
    biggest_loss_bet = lose_bets.order_by('-open_amount').first()
    biggest_loss = -biggest_loss_bet.open_amount if biggest_loss_bet else 0
    
    # 平均投注额
    average_bet = round(total_invested / total_battles, 2) if total_battles > 0 else 0
    
    resp = {
        'total_battles': total_battles,
        'total_wins': total_wins,
        'total_losses': total_losses,
        'win_rate': win_rate,
        'total_invested': total_invested,
        'total_earned': total_earned,
        'net_profit': net_profit,
        'biggest_win': biggest_win,
        'biggest_loss': biggest_loss,
        'average_bet': average_bet
    }
    return RespCode.Succeed.value, resp


def get_room_record(user, fields, page, page_size):
    queryset = CaseRoom.objects.filter(user=user).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CaseRoomHistorySerializer(items, many=True, fields=fields).data
    for item in items_data:
        rounds = item.get('rounds', [])
        case_unique = {}
        for c in rounds:
            case = c.get('case', {})
            name = case.get('name', '')
            # print(name, case_unique)
            if name in case_unique:
                case_unique[name]['count'] += 1
            else:
                case['count'] = 1
                case_unique[name] = case
        item['rounds'] = [{'case': v} for k, v in case_unique.items()]
    resp = {
        'records': items_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_room_history(fields, page, page_size):
    queryset = CaseRoom.objects.filter(private=0, state__in=[
        # GameState.Joinable.value,
        # GameState.Joining.value,
        # GameState.Full.value,
        # GameState.Running.value,
        GameState.End.value
    ]).order_by('-create_time')[:100]
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CaseRoomHistorySerializer(items, many=True, fields=fields).data
    for item in items_data:
        rounds = item.get('rounds', [])
        case_unique = {}
        for c in rounds:
            case = c.get('case', {})
            name = case.get('name', '')
            if name in case_unique:
                case_unique[name]['count'] += 1
            else:
                case['count'] = 1
                case_unique[name] = case
        item['rounds'] = [{'case': v} for k, v in case_unique.items()]

    resp = {
        'records': items_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp

def get_room_record_all(user, fields, page, page_size):
    queryset = CaseRoomBet.objects.filter(user=user).order_by('-create_time')
    # print(queryset)
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CaseRoomBetSerializer(items, many=True, fields=fields).data    
   
    resp = {
        'records': items_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp

def check_pk_bot():
    if get_maintenance() or get_maintenance_box_room():
        _logger.info('系统维护中，对战机器人暂停工作')
        return
    # 获取缓存的时间间隔的值
    pk_bot_interval_time = get_cached_pk_bot_interval_time()
    while True:
        try:
            # if get_maintenance() or get_maintenance_box_room:
            #     _logger.info('PK系统维护中')
            #     break
            if not is_connection_usable():
                connection.close()            
            # bots_list = list(CaseBot.objects.filter(Q(user__extra__box_chance_type='a') | Q(user__extra__box_chance_type='b'),enable=True, user__asset__balance__gt=1000))
            # bots = bots_list[0:100]
            #while True: 
            joinable_rooms_count = CaseRoom.objects.filter(state=GameState.Joinable.value).count()
            if joinable_rooms_count > 10:
                time.sleep(int(pk_bot_interval_time))
                continue
            
            # 直接从缓存中取
            cache_key = 'pk_bot'
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                bots = cached_data  # 从缓存中获取机器人列表
            else:
                # 缓存数量 num
                cache_key_num = 'pk_bot_num_max'
                cached_data_num = cache.get(cache_key_num)
                # 如果存在缓存
                if cached_data_num is not None:
                    num = cached_data_num  # 从缓存中获取数量
                else:
                    num = int(get_pk_bot_num_max())  # 获取机器人数量上限
                    # 缓存数量
                    cache.set(cache_key_num, num, timeout = 60 * 10)
                # 获取机器人列表
                #bots = CaseBot.objects.filter(user__asset__balance__gt=100, enable=True).order_by('?')[:num]
                bots = CaseBot.objects.filter(Q(user__extra__box_chance_type='a') | Q(user__extra__box_chance_type='b') | Q(user__extra__box_chance_type='c'), 
                                              user__asset__balance__gt=100, enable=True).order_by('?')[:num]
                # 缓存
                cache.set(cache_key, bots, timeout = 60 * 10)

            for bot in bots:
                last_open = CaseRecord.objects.filter(user=bot.user).order_by('-create_time').first()
                if last_open:
                    last_open_past = (timezone.now() - last_open.create_time).seconds
                    #if last_open_past < bot.open_idle_min:
                    if last_open_past < 3000:
                        continue
                    elif last_open_past > bot.open_idle_max:
                        open = True
                    else:
                        # 开箱时间间隔随机化
                        open_probability = 1 / (bot.open_idle_max - last_open_past + 1)
                        percentage = numpy.random.uniform(0, 1)
                        open = open_probability > percentage
                else:
                    open = True
                if open:
                    cache_key_box = 'bot_box_pk'
                    cached_data_box = cache.get(cache_key_box)
                    # 如果存在缓存
                    if cached_data_box is not None:
                        case_list = cached_data_box  # 从缓存中获取数量
                    else:
                        # 获取该机器人可用且未锁定的箱子列表
                        case_list = list(Case.objects.filter(enable=True, is_show=True, enable_room=True))
                        cache.set(cache_key_box, case_list, timeout = 60 * 60)

                    type = random.randint(1, 4)  # 随机选择箱子单个还是多个
                    total_cost = 0.0
                    #case_list = list(Case.objects.filter(enable=True, unlock=True, enable_room=True))

                    if case_list:
                        if type == 1:
                            #随机1-4箱子，每个只出现一次形式：A，AB,ABC,ABCD
                            num = random.randint(1, 4)  # 随机选择 1 到 4 个值
                            cases = random.sample(case_list, num)
                            keys = [case.case_key for case in cases]

                            for case in cases:
                                cost = round(case.price * case.discount / 100, 2)
                                total_cost += cost                        
                            if bot.user.asset.balance < total_cost:
                                continue
                            create_case_room(bot.user, keys, random.randint(1, 4), 1, 0)
                            break
                        elif type == 2:
                            # 随机1个箱子，出现2-4次：2A,3A,4A
                            # 随机选择一个箱子
                            selected_case = random.choice(case_list)
                            # 决定这个箱子key将被使用的次数，2到4次
                            num_keys = random.randint(2, 4)
                            # 准备箱子keys，即使是同一个箱子，也重复出现指定次数
                            case_keys = [selected_case.case_key] * num_keys
                            # 随机决定参与人数，1到4人
                            num_joiners = random.randint(1, 4)

                            cost = round(selected_case.price * selected_case.discount / 100, 2) * num_keys
                            if bot.user.asset.balance >= cost:
                                create_case_room(bot.user, case_keys, num_joiners, 1, 0)
                                break
                        elif type == 3:
                            # 随机2箱子个形式：A3B,2A1B,2A2B,3A1B
                            cases = random.sample(case_list, 2)
                            num_a = random.randint(1, 3)
                            num_b = random.randint(1, 3)
                            if num_a + num_b > 4:
                                num_a = 4 - num_b
                            # 创建房间并分配箱子
                            keys_a = [cases[0].case_key] * num_a
                            keys_b = [cases[1].case_key] * num_b
                            create_case_room(bot.user, keys_a + keys_b, random.randint(1, 4), 1, 0)
                            break                            
                        
                        elif type == 4:
                            # 随机3个箱子形式 2ABC,A2BC,AB2C
                            cases = random.sample(case_list, 3)

                            # 随机选择一个箱子使用两次
                            double_use_case = random.choice(cases)
                            
                            # 其他两个箱子必须使用一次
                            single_use_cases = [case for case in cases if case != double_use_case]
                            
                            # 为使用两次的箱子准备两个key
                            double_use_keys = [double_use_case.case_key, double_use_case.case_key]
                            
                            # 为使用一次的箱子各准备一个key
                            single_use_keys = [case.case_key for case in single_use_cases]
                            
                            # 创建房间并分配箱子
                            all_keys = double_use_keys + single_use_keys
                            # 打乱all_keys的次序
                            random.shuffle(all_keys)
                            create_case_room(bot.user, all_keys, random.randint(1, 4), 1, 0)
                            break   
                                                                   
        except ParamException as pe:
            pass
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(int(pk_bot_interval_time)*int(get_time_period()))

def check_pk_join_bot():
    if get_maintenance() or get_maintenance_box_room():
        _logger.info('系统维护中，对战机器人暂停工作')
        return  
    
    
    # 获取缓存的时间间隔的值
    pk_bot_join_interval_time = get_cached_pk_bot_join_interval_time()
    while True:
        try:
            # if get_maintenance() or get_maintenance_box_room:
            #     _logger.info('PK系统维护中')
            #     break
            if not is_connection_usable():
                connection.close()
            
            rooms = CaseRoom.objects.filter(state=GameState.Joinable.value)

            if rooms.count() < 5:
                time.sleep(int(pk_bot_join_interval_time))
                continue      
            # 读取缓存
            cache_key = 'pk_bot'
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                bots = cached_data  # 从缓存中获取机器人列表
            else:
                # 缓存数量 num
                cache_key_num = 'pk_bot_num_max'
                cached_data_num = cache.get(cache_key_num)
                # 如果存在缓存
                if cached_data_num is not None:
                    num = cached_data_num  # 从缓存中获取数量
                else:
                    num = int(get_pk_bot_num_max())  # 获取机器人数量上限
                    # 缓存数量
                    cache.set(cache_key_num, num, timeout = 60 * 10)
                # 获取机器人列表
                bots = CaseBot.objects.filter(Q(user__extra__box_chance_type='a') | Q(user__extra__box_chance_type='b') | Q(user__extra__box_chance_type='c'), 
                                              user__asset__balance__gt=100, enable=True).order_by('?')[:num]
                # 缓存
                cache.set(cache_key, bots, timeout = 60 * 10)
            # bots_list = CaseBot.objects.filter(
            # Q(user__extra__box_chance_type='a') | 
            # Q(user__extra__box_chance_type='b') | Q(user__extra__box_chance_type='c') ,
            # enable=True, 
            # user__asset__balance__gt=100
            # ).order_by('-user__asset__balance')
            
            # bots = bots_list            
            
            for bot in bots:
                last_open = CaseRecord.objects.filter(user=bot.user).order_by('-create_time').first()
                if last_open:
                    last_open_past = (timezone.now() - last_open.create_time).seconds
                    if last_open_past < bot.open_idle_min:
                        continue
                    elif last_open_past > bot.open_idle_max:
                        open = True
                    else:
                        # 开箱时间间隔随机化
                        open_probability = 1 / (bot.open_idle_max - last_open_past + 1)
                        percentage = numpy.random.uniform(0, 1)
                        open = open_probability > percentage
                else:
                    open = True
                if open:
                    rooms_list = list(rooms)
                    if rooms_list:
                        room = random.choice(rooms_list)          
                        if bot.user.asset.balance < room.price:
                            continue
                        join_case_room(bot.user, room.uid)
                        break
        except ParamException as pe:
            pass
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(int(pk_bot_join_interval_time)*int(get_time_period()))



def update_base_count(key, count):
    """
    更新指定 key 的 SiteConfig 对象的 value 字段。
    如果 key 不存在，则创建新的记录并将 value 初始化为 count。
    """
    try:
        # 使用 F() 表达式直接在数据库层面上增加 count 值，避免先查询再保存
        obj, created = SiteConfig.objects.get_or_create(key=key, defaults={'value': count})
        if not created:
            obj.value = F('value') + count
            obj.save(update_fields=['value'])
    except Exception as e:
        _logger.error(f"更新 SiteConfig 错误: key={key}, count={count}, 错误: {e}")

# 根据不同时段调整机器人间隔时间
def get_time_period():
    # 获取当前时间小时
    hour = int(time.strftime('%H', time.localtime()))
    
    # 对小时进行修正，以适应时间段
    hour = (hour + 8) % 24  # 如果小时超过 23，取余数使其在 0-23 范围内
    time_periods = {
        (0, 1): 1.5,
        (2, 3): 2,
        (4, 5): 2.5,
        (6, 7): 1,
        (8, 9): 1.5,
        (10, 11): 2,
        (12, 13): 2.5,
        (14, 15): 2,
        (16, 17): 1.5,
        (18, 19): 2,
        (20, 21): 2,
        (22, 23): 1.5
    }

    # 根据小时和分钟，返回对应的时间段值
    for (start_hour, end_hour), value in time_periods.items():
        if start_hour <= hour < end_hour:
            return value
        else:
            return 2
    #return None

def get_cached_pk_bot_interval_time():
    cache_key = 'pk_bot_interval_time'
    cached_value = cache.get(cache_key)
    if cached_value is not None:
        return cached_value
    else:
        pk_bot_interval_time = int(get_pk_bot_interval_time())
        cache.set(cache_key, pk_bot_interval_time, timeout=300)  # 将计算出的时间间隔存入缓存，设置过期时间为1小时
        return pk_bot_interval_time
    
def get_cached_pk_bot_join_interval_time():
    cache_key = 'pk_bot_join_interval_time'
    cached_value = cache.get(cache_key)
    if cached_value is not None:
        return cached_value
    else:
        pk_bot_join_interval_time = int(get_pk_bot_join_interval_time())
        cache.set(cache_key, pk_bot_join_interval_time, timeout=300)  # 将计算出的时间间隔存入缓存，设置过期时间为1小时
        return pk_bot_join_interval_time
    


def get_battle_list(user, state_list, fields, page, page_size, assigner):
    if not isinstance(state_list, list) or not all(isinstance(state, str) for state in state_list):
        return RespCode.ParamsError.value, None

    # 支持两种格式的状态映射
    state_mapping = {
        # 数字字符串格式
        '1': GameState.Initial.value,
        '2': GameState.Joinable.value,
        '3': GameState.Joining.value,
        '4': GameState.Full.value,
        '5': GameState.Running.value,
        '11': GameState.End.value,
        '20': GameState.Cancelled.value,
        # 字符串格式
        'initial': GameState.Initial.value,
        'joinable': GameState.Joinable.value,
        'joining': GameState.Joining.value,
        'full': GameState.Full.value,
        'running': GameState.Running.value,
        'finished': GameState.End.value,
        'cancelled': GameState.Cancelled.value,
        # 兼容其他可能的格式
        'waiting': GameState.Joinable.value,
        'end': GameState.End.value,
    }

    valid_states = [state_mapping.get(state) for state in state_list if state in state_mapping]

    if not valid_states:
        return RespCode.ParamsError.value, None

    if assigner:
        queryset = CaseRoom.objects.filter(Q(user=user) | Q(bets__user=user), state__in=valid_states).distinct()
    else:
        queryset = CaseRoom.objects.filter(state__in=valid_states)
    
    # 智能排序：优先显示不同状态的房间
    # 使用 Case 表达式来自定义排序优先级
    from django.db.models import Case, When, IntegerField
    
    queryset = queryset.annotate(
        state_priority=Case(
            When(state=GameState.Joinable.value, then=1),  # 等待中最优先
            When(state=GameState.Joining.value, then=2),   # 加入中次优先
            When(state=GameState.Full.value, then=3),      # 已满再次
            When(state=GameState.Running.value, then=4),   # 进行中
            When(state=GameState.End.value, then=5),       # 已结束最后
            default=6,
            output_field=IntegerField()
        )
    ).order_by('state_priority', '-update_time')

    if not queryset.exists():
        return RespCode.NoData.value, None

    paginator = Paginator(queryset, page_size)
    try:
        rooms = paginator.page(page)
    except EmptyPage:
        return RespCode.PageNotFound.value, None

    rooms_data = CaseRoomCacheSerializer(rooms, many=True, fields=fields).data

    resp = {
        'rooms': rooms_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_room_participated(user, query, fields, page, page_size, room_type='all'):
    """
    获取用户参与的对战房间
    
    参数:
    - room_type: 房间类型过滤
      - 'all': 所有参与的房间（创建的 + 加入的）- 默认
      - 'created': 只返回用户创建的房间
      - 'joined': 只返回用户加入的房间（不包括自己创建的）
    """
    all_room_ids = []
    
    if room_type in ['all', 'created']:
        # 获取用户创建的房间ID
        created_rooms = CaseRoom.objects.filter(user=user).values_list('id', flat=True)
        all_room_ids.extend(list(created_rooms))
    
    if room_type in ['all', 'joined']:
        # 获取用户参与的房间ID（通过CaseRoomBet）
        joined_rooms = CaseRoomBet.objects.filter(user=user).values_list('room_id', flat=True)
        
        if room_type == 'joined':
            # 如果只要加入的房间，需要排除自己创建的
            created_room_ids = set(CaseRoom.objects.filter(user=user).values_list('id', flat=True))
            joined_rooms = [room_id for room_id in joined_rooms if room_id not in created_room_ids]
        
        all_room_ids.extend(list(joined_rooms))
    
    # 去重
    all_room_ids = list(set(all_room_ids))
    
    if not all_room_ids:
        # 没有找到任何房间
        resp = {
            'rooms': [],
            'total': 0
        }
        return RespCode.Succeed.value, resp
    
    # 查询所有相关房间
    queryset = CaseRoom.objects.filter(
        id__in=all_room_ids
    )
    
    # 添加排序：等待的最前面，满员的其次、然后对战中的、最后对战结束的按照时间排序
    # 使用 Django 的 Case When 来实现自定义排序
    from django.db.models import Case, When, IntegerField
    
    queryset = queryset.annotate(
        sort_priority=Case(
            When(state=GameState.Joinable.value, then=1),  # 等待中 - 最高优先级
            When(state=GameState.Joining.value, then=2),   # 加入中
            When(state=GameState.Full.value, then=3),      # 已满员 - 其次
            When(state=GameState.Running.value, then=4),   # 对战中 - 然后
            When(state=GameState.End.value, then=5),       # 已结束 - 最后
            When(state=GameState.Cancelled.value, then=6), # 已取消 - 最后
            default=7,
            output_field=IntegerField(),
        )
    ).order_by('sort_priority', '-create_time')  # 先按优先级排序，同优先级按创建时间倒序
    
    # 应用其他查询条件，需要处理state参数的映射
    for key, value in query.items():
        if value:
            if key == 'state':
                # 将字符串状态转换为对应的枚举值
                state_mapping = {
                    'joinable': GameState.Joinable.value,
                    'joining': GameState.Joining.value,
                    'full': GameState.Full.value,
                    'running': GameState.Running.value,
                    'finished': GameState.End.value,
                    'cancelled': GameState.Cancelled.value
                }
                if value in state_mapping:
                    queryset = queryset.filter(state=state_mapping[value])
                else:
                    # 如果传入的是数字，直接使用
                    try:
                        state_value = int(value)
                        queryset = queryset.filter(state=state_value)
                    except ValueError:
                        # 无效的状态值，忽略这个过滤条件
                        pass
            else:
                queryset = queryset.filter(**{key: value})

    paginator = Paginator(queryset, page_size)
    rooms = paginator.page(page)
    rooms_data = CaseRoomSerializer(rooms, many=True, fields=fields).data

    resp = {
        'rooms': rooms_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def safe_image_url(image_field):
    """安全地获取图片URL，防止ImageFieldFile序列化错误"""
    if not image_field:
        return ''
    try:
        if hasattr(image_field, 'url'):
            return image_field.url
        return str(image_field) if image_field else ''
    except (ValueError, AttributeError):
        return ''

def generate_animation_id():
    """生成唯一的动画ID"""
    return f"anim_{int(datetime.now().timestamp() * 1000)}_{str(uuid.uuid4())[:8]}"

def get_room_socket_ids(room_uid):
    """获取房间内所有Socket ID"""
    # 这里应该从Redis或其他存储中获取房间内的Socket ID
    # 暂时返回空列表，实际实现需要根据Socket连接管理逻辑
    # 
    # 实际实现示例（需要根据具体的Socket管理逻辑调整）：
    # try:
    #     r = get_redis()
    #     key = f'room_sockets:{room_uid}'
    #     socket_ids = r.smembers(key)
    #     return [sid.decode() if isinstance(sid, bytes) else sid for sid in socket_ids]
    # except Exception as e:
    #     _logger.error(f'Failed to get socket IDs for room {room_uid}: {e}')
    #     return []
    
    # 为了兼容性，如果没有实现Socket ID管理，则使用None发送给所有连接的客户端
    return [None]

def ws_send_round_start(room_uid, round_number, total_rounds, participants):
    """发送回合开始消息 - 支持时间戳同步和去重"""
    import time
    from .round_manager import BattleRoundValidator
    
    # 🔥 新增：验证轮次合理性
    if not BattleRoundValidator.validate_message_round(room_uid, 'round_start', round_number):
        _logger.error(f"轮次验证失败，消息被拒绝: room={room_uid}, round={round_number}")
        return
    
    # 🔥 新增：检查消息是否已经发送过
    if is_message_already_sent(room_uid, 'round_start', round_number):
        _logger.warning(f"回合开始消息已发送过，跳过重复发送: room={room_uid}, round={round_number}")
        return
    
    # 计算回合开始时间戳
    current_timestamp = int(time.time() * 1000)
    round_start_timestamp = current_timestamp + 1000  # 1秒后开始回合
    
    # 生成消息序列号
    message_sequence = int(time.time() * 1000)
    
    message_data = {
        'round': round_number,
        'total_rounds': total_rounds,
        'round_start_timestamp': round_start_timestamp,  # 回合开始时间
        'server_timestamp': current_timestamp,  # 服务器当前时间
        'message_sequence': message_sequence,  # 消息序列号
        'message_type': 'round_start',  # 消息类型
        'animation_config': {
            'case_animation_duration': 8000,
            'simultaneous_opening': True,
            'reveal_delay': 2000
        },
        'sync_config': {
            'enable_timestamp_sync': True,
            'tolerance_ms': 100
        },
        'participants': participants
    }
    
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'round_start', socket_id)
    
    # 🔥 新增：标记消息已发送
    mark_message_as_sent(room_uid, 'round_start', round_number)
    
    _logger.info(f'回合开始消息发送成功: room={room_uid}, round={round_number}, sequence={message_sequence}')

def ws_send_opening_start(room_uid, animation_id, participants, round_number=None, total_rounds=None):
    """发送开箱动画开始消息 - 支持时间戳同步和去重"""
    import time
    from .round_manager import BattleRoundValidator
    
    # 🔥 新增：验证轮次合理性（如果提供了轮次）
    if round_number is not None and not BattleRoundValidator.validate_message_round(room_uid, 'opening_start', round_number):
        _logger.error(f"轮次验证失败，消息被拒绝: room={room_uid}, round={round_number}")
        return
    
    # 🔥 新增：检查消息是否已经发送过
    if is_message_already_sent(room_uid, 'opening_start', round_number, animation_id):
        _logger.warning(f"开箱动画开始消息已发送过，跳过重复发送: room={room_uid}, round={round_number}, animation_id={animation_id}")
        return
    
    # 计算动画开始时间戳 (当前时间 + 2秒准备时间)
    current_timestamp = int(time.time() * 1000)  # 毫秒级时间戳
    animation_start_timestamp = current_timestamp + 2000  # 2秒后开始动画
    
    # 生成消息序列号
    message_sequence = int(time.time() * 1000)
    
    # 🔥 修复：确保轮次字段始终存在，避免前端收到undefined
    if round_number is None:
        # 如果没有提供轮次，尝试从轮次管理器获取
        try:
            from .round_manager import BattleRoundManager
            round_manager = BattleRoundManager(room_uid)
            round_number = round_manager.get_current_round()
            _logger.info(f"轮次参数为空，从管理器获取: room={room_uid}, round={round_number}")
        except Exception as e:
            _logger.warning(f"无法获取轮次，使用默认值: room={room_uid}, error={e}")
            round_number = 1
    
    if total_rounds is None:
        # 如果没有提供总轮次，尝试从轮次管理器获取
        try:
            from .round_manager import BattleRoundManager
            round_manager = BattleRoundManager(room_uid)
            total_rounds = round_manager.get_total_rounds()
            _logger.info(f"总轮次参数为空，从管理器获取: room={room_uid}, total_rounds={total_rounds}")
        except Exception as e:
            _logger.warning(f"无法获取总轮次，使用默认值: room={room_uid}, error={e}")
            total_rounds = 1
    
    message_data = {
        'animation_id': animation_id,
        'animation_start_timestamp': animation_start_timestamp,  # 绝对开始时间
        'server_timestamp': current_timestamp,  # 服务器当前时间
        'message_sequence': message_sequence,  # 消息序列号
        'message_type': 'opening_start',  # 消息类型
        'preparation_time': 2000,  # 准备时间 (毫秒)
        # 🔥 修复：确保round和total_rounds字段始终存在
        'round': round_number,
        'total_rounds': total_rounds,
        'sync_config': {
            'tolerance_ms': 150,  # 同步容忍度 (提高到150ms以适应高延迟网络)
            'max_delay_compensation': 800,  # 最大延迟补偿 (提高到800ms)
            'enable_client_sync': True,  # 启用客户端时钟同步
            'adaptive_tolerance': True,  # 启用自适应容忍度
            'high_latency_tolerance_ms': 250  # 高延迟网络容忍度
        },
        'participants': participants
    }
    
    # 🔥 新增：缓存动画状态到Redis，用于重连恢复
    cache_animation_state(room_uid, {
        'status': 'opening_animation',
        'animation_id': animation_id,
        'stage': 'case_opening',
        'start_timestamp': animation_start_timestamp,
        'duration': 8000  # 8秒动画时长
    })
    
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'opening_start', socket_id)
        # INFO 级日志：记录向特定 socket 发送 opening_start 消息，方便排查前端收包问题
        _logger.info(
            f'Sent opening_start to socket_id={socket_id} | room_uid={room_uid} | animation_id={animation_id} | round={round_number} | start_ts={animation_start_timestamp} | sequence={message_sequence}'
        )
    
    # 🔥 新增：标记消息已发送
    mark_message_as_sent(room_uid, 'opening_start', round_number, animation_id)

def ws_send_animation_progress(room_uid, animation_id, progress, stage, participants):
    """发送动画进度同步消息（可选）"""
    message_data = {
        'animation_id': animation_id,
        'progress': progress,
        'stage': stage,
        'participants': participants
    }
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'animation_progress', socket_id)

def ws_send_round_result(room_uid, animation_id, results, round_number=None, total_rounds=None):
    """发送回合结果消息 - 支持去重"""
    import time
    from .round_manager import BattleRoundValidator
    
    # 🔥 新增：验证轮次合理性（如果提供了轮次）
    if round_number is not None and not BattleRoundValidator.validate_message_round(room_uid, 'round_result', round_number):
        _logger.error(f"轮次验证失败，消息被拒绝: room={room_uid}, round={round_number}")
        return
    
    # 🔥 新增：检查消息是否已经发送过
    if is_message_already_sent(room_uid, 'round_result', round_number, animation_id):
        _logger.warning(f"回合结果消息已发送过，跳过重复发送: room={room_uid}, round={round_number}, animation_id={animation_id}")
        return
    
    # 生成消息序列号
    message_sequence = int(time.time() * 1000)
    current_timestamp = int(time.time() * 1000)
    
    # 🔥 修复：确保轮次字段始终存在，避免前端收到undefined
    if round_number is None:
        # 如果没有提供轮次，尝试从轮次管理器获取
        try:
            from .round_manager import BattleRoundManager
            round_manager = BattleRoundManager(room_uid)
            round_number = round_manager.get_current_round()
            _logger.info(f"轮次参数为空，从管理器获取: room={room_uid}, round={round_number}")
        except Exception as e:
            _logger.warning(f"无法获取轮次，使用默认值: room={room_uid}, error={e}")
            round_number = 1
    
    if total_rounds is None:
        # 如果没有提供总轮次，尝试从轮次管理器获取
        try:
            from .round_manager import BattleRoundManager
            round_manager = BattleRoundManager(room_uid)
            total_rounds = round_manager.get_total_rounds()
            _logger.info(f"总轮次参数为空，从管理器获取: room={room_uid}, total_rounds={total_rounds}")
        except Exception as e:
            _logger.warning(f"无法获取总轮次，使用默认值: room={room_uid}, error={e}")
            total_rounds = 1
    
    message_data = {
        'animation_id': animation_id,
        'results': results,
        'server_timestamp': current_timestamp,
        'message_sequence': message_sequence,  # 消息序列号
        'message_type': 'round_result',  # 消息类型
        # 🔥 修复：确保round和total_rounds字段始终存在
        'round': round_number,
        'total_rounds': total_rounds
    }
    
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'round_result', socket_id)
    
    # 🔥 新增：标记消息已发送
    mark_message_as_sent(room_uid, 'round_result', round_number, animation_id)
    
    _logger.info(f'回合结果消息发送成功: room={room_uid}, round={round_number}, animation_id={animation_id}, sequence={message_sequence}')

def ws_send_battle_end(room_uid, winner_data, final_results, round_number=None):
    """发送对战结束消息 - 支持去重"""
    import time
    from .round_manager import BattleRoundValidator
    
    # 🔥 新增：验证轮次合理性（如果提供了轮次）
    if round_number is not None and not BattleRoundValidator.validate_message_round(room_uid, 'battle_end', round_number):
        _logger.warning(f"轮次验证失败，但允许对战结束: room={room_uid}, round={round_number}")
        # 对战结束消息允许发送，但使用安全的轮次值
        round_number = BattleRoundValidator.get_safe_round(room_uid, 1)
    
    # 🔥 新增：检查消息是否已经发送过
    if is_message_already_sent(room_uid, 'battle_end', round_number):
        _logger.warning(f"对战结束消息已发送过，跳过重复发送: room={room_uid}, round={round_number}")
        return
    
    # 生成消息序列号
    message_sequence = int(time.time() * 1000)
    current_timestamp = int(time.time() * 1000)
    
    # 🔥 修复：确保轮次字段始终存在，避免前端收到undefined
    if round_number is None:
        # 如果没有提供轮次，尝试从轮次管理器获取
        try:
            from .round_manager import BattleRoundManager
            round_manager = BattleRoundManager(room_uid)
            round_number = round_manager.get_current_round()
            _logger.info(f"对战结束轮次参数为空，从管理器获取: room={room_uid}, round={round_number}")
        except Exception as e:
            _logger.warning(f"无法获取对战结束轮次，使用默认值: room={room_uid}, error={e}")
            round_number = 1
    
    message_data = {
        'winner': winner_data,
        'final_results': final_results,
        'server_timestamp': current_timestamp,
        'message_sequence': message_sequence,  # 消息序列号
        'message_type': 'battle_end',  # 消息类型
        # 🔥 修复：确保round字段始终存在
        'round': round_number,
        'animation_config': {
            'victory_celebration': True,
            'confetti_duration': 3000,
            'result_display_duration': 5000
        }
    }
    
    # 🔥 新增：对战结束后清理动画缓存
    clear_animation_cache(room_uid)
    
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'battle_end', socket_id)
    
    # 🔥 新增：标记消息已发送
    mark_message_as_sent(room_uid, 'battle_end', round_number)
    
    _logger.info(f'对战结束消息发送成功: room={room_uid}, round={round_number}, sequence={message_sequence}')

def prepare_participant_data(bets, room_round):
    """准备参与者数据用于动画"""
    participants = []
    for index, bet in enumerate(bets):
        # 安全获取用户头像URL
        avatar_url = ''
        if hasattr(bet.user, 'profile') and bet.user.profile:
            avatar_url = safe_image_url(bet.user.profile.avatar)
        
        # 安全获取箱子封面URL
        cover_url = safe_image_url(room_round.case.cover)
        
        participant = {
            'user': {
                'username': bet.user.username,
                'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') and bet.user.profile else bet.user.username,
                'avatar': avatar_url
            },
            'case': {
                'case_key': room_round.case.case_key,
                'name': room_round.case.name,
                'name_en': getattr(room_round.case, 'name_en', room_round.case.name),
                'name_zh_hans': getattr(room_round.case, 'name_zh_hans', room_round.case.name),
                'cover': cover_url
            },
            'animation_duration': 8000,
            'start_delay': index * 200  # 错开200ms开始动画
        }
        participants.append(participant)
    return participants

def prepare_round_results(bets, animation_id, room_round=None):
    """准备回合结果数据"""
    results = []
    for bet in bets:
        # 获取该用户在此轮开出的饰品
        # 🔥 修复：CaseRoomItem没有round字段，使用时间来获取最新的物品
        if room_round:
            # 获取最新创建的饰品（当前回合）- 由于没有round字段，获取最近的物品
            round_items = CaseRoomItem.objects.filter(bet=bet).order_by('-create_time')[:1]
        else:
            # 获取最新创建的饰品（当前回合）
            round_items = CaseRoomItem.objects.filter(bet=bet).order_by('-create_time')[:1]
        
        items_data = []
        for index, item in enumerate(round_items):
            # 确保饰品有相关的稀有度和分类信息
            item_rarity_info = {}
            item_category_info = {}
            item_quality_info = {}
            item_exterior_info = {}
            
            if hasattr(item.item_info, 'item_rarity') and item.item_info.item_rarity:
                rarity = item.item_info.item_rarity
                item_rarity_info = {
                    'rarity_id': rarity.rarity_id,
                    'rarity_name': getattr(rarity, 'rarity_name', ''),
                    'rarity_name_en': getattr(rarity, 'rarity_name_en', ''),
                    'rarity_name_zh_hans': getattr(rarity, 'rarity_name_zh_hans', ''),
                    'rarity_color': getattr(rarity, 'rarity_color', '#cccccc')
                }
            
            if hasattr(item.item_info, 'item_category') and item.item_info.item_category:
                category = item.item_info.item_category
                # 安全获取分类图标URL
                icon_url = ''
                if hasattr(category, 'icon') and category.icon:
                    icon_url = safe_image_url(category.icon)
                
                item_category_info = {
                    'cate_id': category.cate_id,
                    'cate_name': getattr(category, 'cate_name', ''),
                    'cate_name_en': getattr(category, 'cate_name_en', ''),
                    'cate_name_zh_hans': getattr(category, 'cate_name_zh_hans', ''),
                    'icon': icon_url
                }
            
            item_data = {
                'uid': str(item.id),
                'item_id': item.item_info.id,
                'name': item.item_info.market_hash_name,
                'name_en': getattr(item.item_info, 'name_en', item.item_info.market_hash_name),
                'name_zh_hans': getattr(item.item_info, 'name_zh_hans', item.item_info.market_hash_name),
                'image': item.item_info.icon_url,
                'item_price': {
                    'price': float(item.price),
                    'update_time': item.item_info.item_price.update_time.isoformat() if hasattr(item.item_info, 'item_price') and item.item_info.item_price else None
                },
                'item_rarity': item_rarity_info,
                'item_category': item_category_info,
                'reveal_order': index + 1,
                'animation_effects': {
                    'particles': True,
                    'glow_effect': True,
                    'sound_effect': determine_sound_effect(item.item_info)
                }
            }
            items_data.append(item_data)
        
        result = {
            'user': {
                'username': bet.user.username,
                'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') else bet.user.username
            },
            'open_amount': float(bet.open_amount or 0),
            'victory': bet.victory,
            'items': items_data
        }
        results.append(result)
    
    return results

def determine_sound_effect(item_info):
    """根据饰品稀有度确定音效类型"""
    if hasattr(item_info, 'item_rarity') and item_info.item_rarity:
        rarity_id = item_info.item_rarity.rarity_id
        if rarity_id >= 6:  # 隐秘级别及以上
            return 'rare_drop'
        elif rarity_id >= 4:  # 保密级别
            return 'uncommon_drop'
    return 'common_drop'

def ws_send_time_sync_request(room_uid):
    """发送时钟同步请求"""
    import time
    
    current_timestamp = int(time.time() * 1000)
    message_data = {
        'sync_request_timestamp': current_timestamp,
        'sync_id': f"sync_{current_timestamp}_{str(uuid.uuid4())[:8]}"
    }
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'time_sync_request', socket_id)

def get_redis_connection(alias='default'):
    """获取Redis连接

    Args:
        alias: Redis连接别名，兼容原有调用（通常是'default'）
    """
    try:
        import redis
        from django.conf import settings

        # 优先使用配置文件中的Redis设置，否则使用默认值
        redis_config = getattr(settings, 'REDIS_CONFIG', {
            'host': getattr(settings, 'REDIS_HOST', 'redis'),
            'port': getattr(settings, 'REDIS_PORT', 6379),
            'db': getattr(settings, 'REDIS_DB_INDEX', 0),
            'password': getattr(settings, 'REDIS_PASSWORD', None),
            'decode_responses': True,
            'socket_timeout': 5,
            'socket_connect_timeout': 5
        })

        # 如果需要支持多个Redis数据库，可以根据alias调整配置
        # 目前先兼容现有调用，使用默认配置

        return redis.Redis(**redis_config)

    except Exception as e:
        _logger.error(f"Redis连接创建失败: {str(e)}")
        return None

def cache_animation_state(room_uid, animation_data):
    """缓存动画状态到Redis"""
    # 参数验证
    if not room_uid or not isinstance(room_uid, str):
        _logger.warning("cache_animation_state: room_uid参数无效")
        return False
        
    if not animation_data or not isinstance(animation_data, dict):
        _logger.warning("cache_animation_state: animation_data参数无效")
        return False
    
    try:
        import json, time
        
        # 获取Redis连接
        r = get_redis_connection()
        if not r:
            return False
        
        cache_key = f"battle_animation:{room_uid}"
        
        # 构建缓存数据
        cache_data = {
            'status': animation_data.get('status'),
            'animation_id': animation_data.get('animation_id'),
            'stage': animation_data.get('stage'),
            'start_timestamp': animation_data.get('start_timestamp'),
            'duration': animation_data.get('duration', 8000),
            'cached_at': int(time.time())  # 添加缓存时间戳 (秒级)
        }
        
        # 移除None值
        cache_data = {k: v for k, v in cache_data.items() if v is not None}
        
        # 缓存10分钟 (600秒) - 使用set with ex参数替代setex
        success = r.set(cache_key, json.dumps(cache_data), ex=600)
        
        if success:
            _logger.info(f"动画状态缓存成功: room_uid={room_uid}, status={cache_data.get('status')}")
            return True
        else:
            _logger.warning(f"动画状态缓存失败: room_uid={room_uid}")
            return False
            
    except Exception as e:
        if 'redis' in str(e).lower():
            _logger.error(f"Redis缓存失败: {str(e)}, room_uid: {room_uid}")
        else:
            _logger.warning(f"缓存动画状态失败: {str(e)}, room_uid: {room_uid}")
        return False

def clear_animation_cache(room_uid):
    """清除动画状态缓存"""
    if not room_uid or not isinstance(room_uid, str):
        return False
        
    try:
        r = get_redis_connection()
        if not r:
            return False
        
        cache_key = f"battle_animation:{room_uid}"
        result = r.delete(cache_key)
        
        if result:
            _logger.info(f"动画缓存清除成功: room_uid={room_uid}")
            return True
        else:
            _logger.info(f"动画缓存不存在或已清除: room_uid={room_uid}")
            return False
            
    except Exception as e:
        _logger.warning(f"清除动画缓存失败: {str(e)}, room_uid: {room_uid}")
        return False

def ws_send_animation_progress(room_uid, animation_id, progress, stage, participants):
    """发送动画进度同步消息（可选）- 支持时间戳"""
    import time
    
    current_timestamp = int(time.time() * 1000)
    
    message_data = {
        'animation_id': animation_id,
        'progress': progress,
        'stage': stage,
        'server_timestamp': current_timestamp,  # 服务器时间戳
        'sync_config': {
            'enable_progress_sync': True,
            'sync_interval_ms': 500  # 进度同步间隔
        },
        'participants': participants
    }
    socket_ids = get_room_socket_ids(room_uid)
    for socket_id in socket_ids:
        ws_send_box_room_detail(message_data, 'animation_progress', socket_id)




