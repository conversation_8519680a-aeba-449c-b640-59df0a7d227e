#!/bin/bash
# 系统诊断脚本 - 诊断Django环境问题

echo "=== 系统诊断开始 ==="
echo "时间: $(date)"
echo

# 1. 检查系统资源
echo "1. 系统资源检查:"
echo "内存使用情况:"
free -h
echo
echo "磁盘使用情况:"
df -h
echo
echo "CPU负载:"
uptime
echo

# 2. 检查进程状态
echo "2. Python进程检查:"
echo "Python相关进程:"
ps aux | grep python | head -10
echo
echo "Django进程:"
ps aux | grep manage.py
echo
echo "总进程数:"
ps aux | wc -l
echo

# 3. 检查网络和服务
echo "3. 服务状态检查:"
echo "MySQL服务:"
systemctl status mysql 2>/dev/null || systemctl status mysqld 2>/dev/null || echo "MySQL服务状态未知"
echo
echo "Redis服务:"
systemctl status redis 2>/dev/null || echo "Redis服务状态未知"
echo
echo "网络连接:"
netstat -tlnp | grep :3306  # MySQL
netstat -tlnp | grep :6379  # Redis
echo

# 4. 检查日志文件大小
echo "4. 日志文件检查:"
echo "Django日志:"
find /www/wwwroot/csgoskins.com.cn/server/logs -name "*.log" -exec ls -lh {} \; 2>/dev/null || echo "日志目录不存在或无权限"
echo
echo "系统日志:"
ls -lh /var/log/messages 2>/dev/null || ls -lh /var/log/syslog 2>/dev/null || echo "系统日志未找到"
echo

# 5. 检查Django环境
echo "5. Django环境检查:"
cd /www/wwwroot/csgoskins.com.cn/server
echo "当前目录: $(pwd)"
echo "虚拟环境检查:"
if [ -f "venv/bin/python" ]; then
    echo "虚拟环境存在"
    echo "Python版本: $(venv/bin/python --version 2>&1)"
    echo "Django版本: $(venv/bin/python -c 'import django; print(django.get_version())' 2>&1)"
else
    echo "虚拟环境不存在"
fi
echo

# 6. 检查数据库连接
echo "6. 数据库连接检查:"
if command -v mysql >/dev/null 2>&1; then
    echo "尝试连接MySQL..."
    mysql -u root -p -e "SELECT 1;" 2>&1 | head -3
else
    echo "MySQL客户端未安装"
fi
echo

# 7. 检查文件权限
echo "7. 文件权限检查:"
echo "项目目录权限:"
ls -ld /www/wwwroot/csgoskins.com.cn/server
echo "manage.py权限:"
ls -l /www/wwwroot/csgoskins.com.cn/server/manage.py
echo

# 8. 检查运行中的Django进程详情
echo "8. Django进程详情:"
pgrep -f "manage.py" | while read pid; do
    echo "进程 $pid:"
    ps -p $pid -o pid,ppid,cmd,etime,pcpu,pmem
    echo "打开的文件数: $(lsof -p $pid 2>/dev/null | wc -l)"
    echo
done

echo "=== 系统诊断完成 ==="
echo
echo "=== 建议的修复步骤 ==="
echo "1. 如果内存不足，重启服务器或清理内存"
echo "2. 如果有僵尸Django进程，使用 pkill -f manage.py 清理"
echo "3. 如果磁盘空间不足，清理日志文件"
echo "4. 如果数据库连接异常，重启MySQL服务"
echo "5. 如果Redis异常，重启Redis服务"
