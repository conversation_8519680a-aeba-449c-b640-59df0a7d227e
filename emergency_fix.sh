#!/bin/bash
# 紧急修复脚本 - 解决Django环境问题并修复卡住的对战

echo "=== 紧急修复开始 ==="
echo "时间: $(date)"
echo

# 1. 清理可能的僵尸进程
echo "1. 清理Django进程..."
pkill -f "manage.py" 2>/dev/null
pkill -f "python.*django" 2>/dev/null
sleep 2
echo "Django进程已清理"

# 2. 检查并重启关键服务
echo "2. 检查关键服务..."
if ! systemctl is-active --quiet mysql && ! systemctl is-active --quiet mysqld; then
    echo "重启MySQL服务..."
    systemctl restart mysql 2>/dev/null || systemctl restart mysqld 2>/dev/null
fi

if ! systemctl is-active --quiet redis; then
    echo "重启Redis服务..."
    systemctl restart redis 2>/dev/null
fi

# 3. 清理临时文件和日志
echo "3. 清理临时文件..."
cd /www/wwwroot/csgoskins.com.cn/server

# 清理Python缓存
find . -name "*.pyc" -delete 2>/dev/null
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null

# 清理大日志文件（保留最近的）
if [ -d "logs" ]; then
    find logs -name "*.log" -size +100M -mtime +1 -exec truncate -s 0 {} \; 2>/dev/null
fi

echo "临时文件已清理"

# 4. 直接使用SQL修复卡住的对战
echo "4. 修复卡住的对战房间..."

# 创建SQL修复脚本
cat > fix_battles.sql << 'EOF'
-- 修复Running状态卡住的对战房间

-- 查看当前状态
SELECT 'Current Running Rooms:' as info;
SELECT COUNT(*) as running_count FROM box_caseroom WHERE state = 5 AND type IN (1, 3);

-- 修复所有轮次已完成但仍在Running状态的房间
UPDATE box_caseroom cr
SET state = 11
WHERE cr.state = 5 
  AND cr.type IN (1, 3)
  AND NOT EXISTS (
      SELECT 1 FROM box_caseroomround crr 
      WHERE crr.room_id = cr.id AND crr.opened = 0
  )
  AND EXISTS (
      SELECT 1 FROM box_caseroomround crr 
      WHERE crr.room_id = cr.id
  );

-- 强制结束超过2小时的Running房间
UPDATE box_caseroom 
SET state = 11 
WHERE state = 5 
  AND type IN (1, 3) 
  AND update_time < DATE_SUB(NOW(), INTERVAL 2 HOUR);

-- 取消无参与者的房间
UPDATE box_caseroom cr
SET state = 20
WHERE cr.state = 5 
  AND cr.type IN (1, 3)
  AND NOT EXISTS (
      SELECT 1 FROM box_caseroombbet bet 
      WHERE bet.room_id = cr.id
  );

-- 为结束的房间设置获胜者
UPDATE box_caseroombbet bet
JOIN (
    SELECT 
        room_id,
        id as winner_id
    FROM (
        SELECT 
            room_id,
            id,
            ROW_NUMBER() OVER (PARTITION BY room_id ORDER BY open_amount DESC, id ASC) as rn
        FROM box_caseroombbet
        WHERE room_id IN (
            SELECT id FROM box_caseroom 
            WHERE state = 11 AND type IN (1, 3)
            AND update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        )
    ) ranked
    WHERE rn = 1
) winners ON bet.id = winners.winner_id
SET bet.victory = 1,
    bet.win_amount = (
        SELECT SUM(COALESCE(b.open_amount, 0)) 
        FROM box_caseroombbet b 
        WHERE b.room_id = bet.room_id
    );

-- 确保非获胜者状态正确
UPDATE box_caseroombbet 
SET victory = 0,
    win_amount = COALESCE(win_amount, 0),
    win_items_count = COALESCE(win_items_count, 0)
WHERE room_id IN (
    SELECT id FROM box_caseroom 
    WHERE state IN (11, 20) AND type IN (1, 3)
)
AND victory != 1;

-- 查看修复后状态
SELECT 'After Fix:' as info;
SELECT 
    state,
    COUNT(*) as count,
    CASE state
        WHEN 1 THEN 'Initial'
        WHEN 2 THEN 'Joinable'
        WHEN 4 THEN 'Full'
        WHEN 5 THEN 'Running'
        WHEN 11 THEN 'End'
        WHEN 20 THEN 'Cancelled'
        ELSE CONCAT('State_', state)
    END as state_name
FROM box_caseroom 
WHERE type IN (1, 3)
GROUP BY state 
ORDER BY state;
EOF

# 执行SQL修复
if command -v mysql >/dev/null 2>&1; then
    echo "执行SQL修复..."
    mysql -u root -p < fix_battles.sql 2>/dev/null || echo "SQL执行需要手动输入密码"
else
    echo "MySQL客户端未找到，请手动执行 fix_battles.sql"
fi

# 5. 测试Django环境
echo "5. 测试Django环境..."
cd /www/wwwroot/csgoskins.com.cn/server

if [ -f "venv/bin/python" ]; then
    echo "使用虚拟环境测试..."
    timeout 10 venv/bin/python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
import django
django.setup()
from box.models import CaseRoom
print('Django环境正常')
print('Running房间数:', CaseRoom.objects.filter(state=5, type__in=[1,3]).count())
" 2>&1 || echo "Django环境仍有问题"
else
    echo "虚拟环境不存在"
fi

echo
echo "=== 紧急修复完成 ==="
echo
echo "=== 后续步骤 ==="
echo "1. 检查系统资源是否正常"
echo "2. 重启Django应用服务"
echo "3. 监控新的对战房间是否正常"
echo "4. 如果问题持续，考虑重启服务器"
echo
echo "=== 手动验证命令 ==="
echo "检查对战状态: cd /www/wwwroot/csgoskins.com.cn/server && venv/bin/python manage.py shell"
echo "然后执行: from box.models import CaseRoom; print(CaseRoom.objects.filter(state=5).count())"
