#!/usr/bin/env python3
"""
直接连接阿里云RDS修复卡住的对战房间
不依赖Django环境
"""
import pymysql
import sys
from datetime import datetime

# 阿里云RDS连接配置 - 请根据实际情况修改
RDS_CONFIG = {
    'host': 'your-rds-host.mysql.rds.aliyuncs.com',  # 请修改为您的RDS地址
    'port': 3306,
    'user': 'your_username',  # 请修改为您的用户名
    'password': 'your_password',  # 请修改为您的密码
    'database': 'your_database',  # 请修改为您的数据库名
    'charset': 'utf8mb4'
}

def connect_rds():
    """连接阿里云RDS"""
    try:
        connection = pymysql.connect(**RDS_CONFIG)
        print("✓ 成功连接到阿里云RDS")
        return connection
    except Exception as e:
        print(f"✗ 连接RDS失败: {e}")
        return None

def execute_query(connection, query, description=""):
    """执行SQL查询"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                return results
            else:
                connection.commit()
                return cursor.rowcount
    except Exception as e:
        print(f"✗ 执行SQL失败 {description}: {e}")
        return None

def show_current_status(connection):
    """显示当前对战房间状态"""
    print("\n=== 当前对战房间状态 ===")
    
    query = """
    SELECT 
        state,
        COUNT(*) as count,
        CASE state
            WHEN 1 THEN 'Initial'
            WHEN 2 THEN 'Joinable'
            WHEN 3 THEN 'Joining'
            WHEN 4 THEN 'Full'
            WHEN 5 THEN 'Running'
            WHEN 11 THEN 'End'
            WHEN 20 THEN 'Cancelled'
            ELSE CONCAT('Unknown_', state)
        END as state_name
    FROM box_caseroom 
    WHERE type IN (1, 3)
    GROUP BY state 
    ORDER BY state
    """
    
    results = execute_query(connection, query, "查询房间状态")
    if results:
        for row in results:
            state, count, state_name = row
            print(f"状态 {state} ({state_name}): {count} 个房间")

def show_running_details(connection):
    """显示Running状态房间详情"""
    print("\n=== Running状态房间详情 ===")
    
    query = """
    SELECT 
        cr.short_id,
        cr.create_time,
        cr.update_time,
        TIMESTAMPDIFF(MINUTE, cr.update_time, NOW()) as minutes_stuck,
        (SELECT COUNT(*) FROM box_caseroombbet WHERE room_id = cr.id) as bet_count,
        (SELECT COUNT(*) FROM box_caseroomround WHERE room_id = cr.id AND opened = 0) as unopened_rounds
    FROM box_caseroom cr
    WHERE cr.state = 5 AND cr.type IN (1, 3)
    ORDER BY cr.update_time
    LIMIT 10
    """
    
    results = execute_query(connection, query, "查询Running房间")
    if results:
        print(f"发现 {len(results)} 个Running状态的房间:")
        for row in results:
            short_id, create_time, update_time, minutes_stuck, bet_count, unopened_rounds = row
            print(f"  房间 {short_id}: 卡住 {minutes_stuck} 分钟, {bet_count} 个参与者, {unopened_rounds} 个未开启轮次")
    else:
        print("没有发现Running状态的房间")

def fix_completed_rounds_rooms(connection):
    """修复轮次已完成但仍在Running状态的房间"""
    print("\n=== 修复轮次已完成的房间 ===")
    
    query = """
    UPDATE box_caseroom cr
    SET state = 11, update_time = NOW()
    WHERE cr.state = 5 
      AND cr.type IN (1, 3)
      AND NOT EXISTS (
          SELECT 1 FROM box_caseroomround crr 
          WHERE crr.room_id = cr.id AND crr.opened = 0
      )
      AND EXISTS (
          SELECT 1 FROM box_caseroomround crr 
          WHERE crr.room_id = cr.id
      )
    """
    
    affected = execute_query(connection, query, "修复轮次已完成的房间")
    if affected is not None:
        print(f"✓ 修复了 {affected} 个轮次已完成的房间")

def fix_timeout_rooms(connection):
    """修复超时房间"""
    print("\n=== 修复超时房间 ===")
    
    # 取消无参与者的超时房间
    query1 = """
    UPDATE box_caseroom cr
    SET state = 20, update_time = NOW()
    WHERE cr.state = 5 
      AND cr.type IN (1, 3)
      AND cr.update_time < DATE_SUB(NOW(), INTERVAL 4 HOUR)
      AND NOT EXISTS (
          SELECT 1 FROM box_caseroombbet bet 
          WHERE bet.room_id = cr.id
      )
    """
    
    affected1 = execute_query(connection, query1, "取消无参与者的超时房间")
    if affected1 is not None:
        print(f"✓ 取消了 {affected1} 个无参与者的超时房间")
    
    # 强制结束超过6小时的房间
    query2 = """
    UPDATE box_caseroom 
    SET state = 11, update_time = NOW()
    WHERE state = 5 
      AND type IN (1, 3) 
      AND update_time < DATE_SUB(NOW(), INTERVAL 6 HOUR)
    """
    
    affected2 = execute_query(connection, query2, "强制结束超时房间")
    if affected2 is not None:
        print(f"✓ 强制结束了 {affected2} 个超时房间")

def set_winners(connection):
    """为结束的房间设置获胜者"""
    print("\n=== 设置获胜者 ===")
    
    # 重置参与者状态
    query1 = """
    UPDATE box_caseroombbet 
    SET victory = 0,
        win_amount = COALESCE(win_amount, 0),
        win_items_count = COALESCE(win_items_count, 0)
    WHERE room_id IN (
        SELECT id FROM box_caseroom 
        WHERE state = 11 AND type IN (1, 3)
        AND update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    )
    """
    
    execute_query(connection, query1, "重置参与者状态")
    
    # 设置获胜者
    query2 = """
    UPDATE box_caseroombbet bet
    JOIN (
        SELECT 
            room_id,
            id as winner_bet_id
        FROM (
            SELECT 
                room_id,
                id,
                open_amount,
                ROW_NUMBER() OVER (PARTITION BY room_id ORDER BY open_amount DESC, id ASC) as rn
            FROM box_caseroombbet
            WHERE room_id IN (
                SELECT id FROM box_caseroom 
                WHERE state = 11 AND type IN (1, 3)
                AND update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            )
        ) ranked
        WHERE rn = 1
    ) winners ON bet.id = winners.winner_bet_id
    SET bet.victory = 1,
        bet.win_amount = (
            SELECT SUM(COALESCE(b.open_amount, 0)) 
            FROM box_caseroombbet b 
            WHERE b.room_id = bet.room_id
        )
    """
    
    affected = execute_query(connection, query2, "设置获胜者")
    if affected is not None:
        print(f"✓ 设置了 {affected} 个获胜者")

def main():
    """主函数"""
    print("=== 阿里云RDS对战房间修复工具 ===")
    print(f"开始时间: {datetime.now()}")
    
    # 检查pymysql是否安装
    try:
        import pymysql
    except ImportError:
        print("✗ 请先安装pymysql: pip install pymysql")
        return
    
    # 连接数据库
    connection = connect_rds()
    if not connection:
        print("请检查RDS连接配置")
        return
    
    try:
        # 显示当前状态
        show_current_status(connection)
        show_running_details(connection)
        
        # 确认是否继续
        if len(sys.argv) > 1 and sys.argv[1] == '--auto':
            proceed = True
        else:
            proceed = input("\n是否继续修复? (y/N): ").lower() == 'y'
        
        if not proceed:
            print("取消修复操作")
            return
        
        # 执行修复
        fix_completed_rounds_rooms(connection)
        fix_timeout_rooms(connection)
        set_winners(connection)
        
        # 显示修复后状态
        print("\n=== 修复后状态 ===")
        show_current_status(connection)
        show_running_details(connection)
        
        print(f"\n✓ 修复完成: {datetime.now()}")
        
    finally:
        connection.close()
        print("✓ 数据库连接已关闭")

if __name__ == "__main__":
    main()
